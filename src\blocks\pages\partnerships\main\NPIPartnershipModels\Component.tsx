'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPI<PERSON>ard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { PartnershipApplicationModal } from '@/components/ui/npi-partnership-modal'
import Link from 'next/link'
import {
  Building,
  Users,
  Globe,
  Lightbulb,
  Heart,
  TrendingUp,
  ArrowRight,
  CheckCircle,
  Target,
} from 'lucide-react'

interface PartnershipModel {
  title: string
  description: string
  icon: React.ReactNode
  color: string
  benefits: string[]
  requirements: string[]
  examples: string[]
  commitmentLevel: 'Low' | 'Medium' | 'High'
  duration: string
  investmentRange: string
}

interface NPIPartnershipModelsProps {
  title?: string
  description?: string
  models?: PartnershipModel[]
}

export const NPIPartnershipModelsBlock: React.FC<NPIPartnershipModelsProps> = ({
  title = 'Partnership Models',
  description = 'Discover flexible partnership frameworks designed to maximize impact while respecting community rights and ensuring sustainable development.',
  models = [
    {
      title: 'Public-Private Partnership (PPP)',
      description:
        'Collaborative framework between government agencies, private sector, and NPI for large-scale natural products development.',
      icon: <Building className="w-8 h-8" />,
      color: 'from-primary to-primary/90',
      benefits: [
        'Access to government support and policy backing',
        'Shared risk and investment burden',
        'Regulatory compliance and legal protection',
        'Large-scale market access and distribution',
      ],
      requirements: [
        'Minimum investment of KES 100M',
        'Commitment to community benefit-sharing',
        'Environmental and social compliance',
        '5-year minimum partnership duration',
      ],
      examples: [
        'Aloe Vera Processing Facility (Baringo)',
        'Medicinal Plants Research Center (Nairobi)',
        'Essential Oils Export Hub (Mombasa)',
      ],
      commitmentLevel: 'High',
      duration: '5-10 years',
      investmentRange: 'KES 100M+',
    },
    {
      title: 'Community-Based Partnership',
      description:
        'Direct collaboration with local communities for grassroots natural products development and knowledge preservation.',
      icon: <Users className="w-8 h-8" />,
      color: 'from-secondary to-secondary/90',
      benefits: [
        'Direct community engagement and ownership',
        'Authentic traditional knowledge access',
        'Strong local support and sustainability',
        'Cultural preservation and empowerment',
      ],
      requirements: [
        'Community consent and participation',
        'Respect for traditional knowledge protocols',
        'Fair benefit-sharing agreements',
        'Cultural sensitivity training',
      ],
      examples: [
        'Ogiek Honey Cooperative (Nakuru)',
        'Maasai Traditional Medicine (Kajiado)',
        'Turkana Moringa Initiative',
      ],
      commitmentLevel: 'Medium',
      duration: '3-7 years',
      investmentRange: 'KES 1M-10M',
    },
    {
      title: 'Research & Academic Collaboration',
      description:
        'Partnership with universities and research institutions for scientific validation and innovation in natural products.',
      icon: <Lightbulb className="w-8 h-8" />,
      color: 'from-accent to-accent/90',
      benefits: [
        'Scientific validation and credibility',
        'Access to research facilities and expertise',
        'Publication and IP development opportunities',
        'Student and researcher engagement',
      ],
      requirements: [
        'Research ethics approval',
        'Data sharing agreements',
        'Publication and IP protocols',
        'Community consent for research',
      ],
      examples: [
        'University of Nairobi Medicinal Plants Study',
        'ICIPE Natural Products Research',
        'Kenyatta University Nutrition Studies',
      ],
      commitmentLevel: 'Medium',
      duration: '2-5 years',
      investmentRange: 'KES 5M-50M',
    },
    {
      title: 'International Development Partnership',
      description:
        'Collaboration with international organizations and donors for capacity building and knowledge exchange.',
      icon: <Globe className="w-8 h-8" />,
      color: 'from-npi-burgundy-500 to-npi-burgundy-600',
      benefits: [
        'Access to international funding',
        'Global knowledge and best practices',
        'Capacity building and training',
        'International market access',
      ],
      requirements: [
        'Alignment with development goals',
        'Transparency and accountability',
        'Local capacity building focus',
        'Sustainable development principles',
      ],
      examples: [
        'World Bank Natural Products Initiative',
        'GIZ Biodiversity Conservation Project',
        'UNIDO Industrial Development Program',
      ],
      commitmentLevel: 'High',
      duration: '3-8 years',
      investmentRange: 'KES 50M-500M',
    },
    {
      title: 'Social Impact Investment',
      description:
        'Investment model focused on generating positive social and environmental impact alongside financial returns.',
      icon: <Heart className="w-8 h-8" />,
      color: 'from-npi-gold-500 to-npi-gold-600',
      benefits: [
        'Measurable social and environmental impact',
        'Flexible financing terms',
        'Patient capital for long-term development',
        'Impact measurement and reporting',
      ],
      requirements: [
        'Clear impact metrics and targets',
        'Regular impact reporting',
        'Community benefit demonstration',
        'Environmental sustainability',
      ],
      examples: [
        "Women's Cooperative Microfinance",
        'Youth Enterprise Development Fund',
        'Community Forest Conservation Bonds',
      ],
      commitmentLevel: 'Medium',
      duration: '3-6 years',
      investmentRange: 'KES 10M-100M',
    },
    {
      title: 'Technology & Innovation Partnership',
      description:
        'Collaboration with technology companies for digital solutions and innovation in natural products sector.',
      icon: <TrendingUp className="w-8 h-8" />,
      color: 'from-npi-green-500 to-npi-green-600',
      benefits: [
        'Access to cutting-edge technology',
        'Digital transformation support',
        'Innovation and efficiency gains',
        'Market reach and scalability',
      ],
      requirements: [
        'Technology transfer agreements',
        'Local capacity building',
        'Data privacy and security',
        'Affordable technology access',
      ],
      examples: [
        'IKIA Digital Platform Development',
        'Blockchain Supply Chain Tracking',
        'AI-Powered Quality Control Systems',
      ],
      commitmentLevel: 'Medium',
      duration: '2-4 years',
      investmentRange: 'KES 20M-200M',
    },
  ],
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedModel, setSelectedModel] = useState('')

  const getCommitmentColor = (level: string) => {
    switch (level) {
      case 'Low':
        return 'bg-green-100 text-green-800'
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'High':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handlePartnershipApplication = (modelTitle?: string) => {
    setSelectedModel(modelTitle || '')
    setIsModalOpen(true)
  }

  return (
    <NPISection className="bg-tertiary">
      <NPISectionHeader>
        <NPISectionTitle className="text-primary">{title}</NPISectionTitle>
        <NPISectionDescription className="text-foreground">{description}</NPISectionDescription>
      </NPISectionHeader>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {models.map((model, index) => (
          <NPICard
            key={index}
            className="overflow-hidden hover:shadow-xl transition-all duration-300 h-full flex flex-col aspect-square"
          >
            {/* Header with gradient */}
            <div className={`bg-gradient-to-r ${model.color} p-3 text-white flex-shrink-0`}>
              <div className="flex flex-col items-center text-center mb-2">
                <div className="bg-white/20 p-1.5 mb-1.5">{model.icon}</div>
                <NPICardTitle className="text-white text-sm mb-1 leading-tight">
                  {model.title}
                </NPICardTitle>
                <div className="flex flex-col gap-0.5">
                  <span
                    className={`px-1.5 py-0.5 text-xs font-medium ${getCommitmentColor(model.commitmentLevel)} bg-white/20 text-white`}
                  >
                    {model.commitmentLevel} Commitment
                  </span>
                  <span className="text-white/80 text-xs font-npi">{model.duration}</span>
                </div>
              </div>
              <p className="text-white/90 text-xs leading-tight font-npi line-clamp-2">
                {model.description}
              </p>
            </div>

            {/* Content */}
            <NPICardContent className="p-2 flex-1 flex flex-col">
              <div className="space-y-2 flex-1">
                {/* Benefits */}
                <div>
                  <h4 className="font-semibold mb-1 text-foreground font-npi text-xs flex items-center gap-1">
                    <CheckCircle className="w-3 h-3 text-secondary" />
                    Benefits
                  </h4>
                  <ul className="space-y-0.5">
                    {model.benefits.slice(0, 3).map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-start gap-1 text-xs">
                        <div className="w-1 h-1 bg-secondary mt-1 flex-shrink-0"></div>
                        <span className="text-muted-foreground font-npi leading-tight text-xs">
                          {benefit}
                        </span>
                      </li>
                    ))}
                    {model.benefits.length > 3 && (
                      <li className="text-xs text-muted-foreground/70 font-npi italic">
                        +{model.benefits.length - 3} more benefits
                      </li>
                    )}
                  </ul>
                </div>

                {/* Requirements */}
                <div>
                  <h4 className="font-semibold mb-1 text-foreground font-npi text-xs flex items-center gap-1">
                    <Target className="w-3 h-3 text-primary" />
                    Requirements
                  </h4>
                  <ul className="space-y-0.5">
                    {model.requirements.slice(0, 3).map((requirement, reqIndex) => (
                      <li key={reqIndex} className="flex items-start gap-1 text-xs">
                        <div className="w-1 h-1 bg-primary mt-1 flex-shrink-0"></div>
                        <span className="text-muted-foreground font-npi leading-tight text-xs">
                          {requirement}
                        </span>
                      </li>
                    ))}
                    {model.requirements.length > 3 && (
                      <li className="text-xs text-muted-foreground/70 font-npi italic">
                        +{model.requirements.length - 3} more requirements
                      </li>
                    )}
                  </ul>
                </div>

                {/* Investment Range */}
                <div className="bg-muted/30 p-2 border border-border/50">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-foreground font-npi">Investment</span>
                    <span className="text-xs font-bold text-primary font-npi">
                      {model.investmentRange}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-2 pt-2 border-t border-border">
                <NPIButton
                  variant="outline"
                  size="sm"
                  className="w-full text-xs py-1"
                  onClick={() => handlePartnershipApplication(model.title)}
                >
                  Apply <ArrowRight className="w-3 h-3 ml-1" />
                </NPIButton>
              </div>
            </NPICardContent>
          </NPICard>
        ))}
      </div>

      {/* Call to Action */}
      <div className="mt-12 text-center">
        <NPICard className="bg-gradient-to-r from-primary/5 to-accent/5 border-2 border-primary/20">
          <NPICardContent className="p-8">
            <h3 className="text-2xl font-bold mb-4 font-npi text-primary">
              Ready to Partner with NPI?
            </h3>
            <p className="text-foreground mb-6 max-w-2xl mx-auto font-npi">
              Join us in transforming Kenya&apos;s natural products sector. Our flexible partnership
              models ensure that every collaboration creates lasting value for communities,
              partners, and the environment.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <NPIButton size="lg" variant="primary" onClick={() => handlePartnershipApplication()}>
                Start Partnership Application
              </NPIButton>
              <NPIButton asChild size="lg" variant="outline">
                <Link href="/contact">Schedule Consultation</Link>
              </NPIButton>
            </div>
          </NPICardContent>
        </NPICard>
      </div>

      {/* Partnership Application Modal */}
      <PartnershipApplicationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        selectedModel={selectedModel}
      />
    </NPISection>
  )
}
