import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { NPIButton } from '@/components/ui/npi-button'

describe('NPIButton', () => {
  it('renders with default props', () => {
    render(<NPIButton>Test Button</NPIButton>)
    const button = screen.getByRole('button', { name: /test button/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('bg-primary')
  })

  it('renders with different variants', () => {
    const { rerender } = render(<NPIButton variant="brown"><PERSON>ton</NPIButton>)
    expect(screen.getByRole('button')).toHaveClass('bg-brown')

    rerender(<NPIButton variant="green">Green Button</NPIButton>)
    expect(screen.getByRole('button')).toHaveClass('bg-green')

    rerender(<NPIButton variant="outline">Outline Button</NPIButton>)
    expect(screen.getByRole('button')).toHaveClass('border-primary')
  })

  it('renders with different sizes', () => {
    const { rerender } = render(<NPIButton size="sm">Small Button</NPIButton>)
    expect(screen.getByRole('button')).toHaveClass('h-8')

    rerender(<NPIButton size="lg">Large Button</NPIButton>)
    expect(screen.getByRole('button')).toHaveClass('h-12')
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<NPIButton onClick={handleClick}>Clickable Button</NPIButton>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('can be disabled', () => {
    const handleClick = jest.fn()
    render(
      <NPIButton disabled onClick={handleClick}>
        Disabled Button
      </NPIButton>
    )
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    
    fireEvent.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('applies custom className', () => {
    render(<NPIButton className="custom-class">Custom Button</NPIButton>)
    expect(screen.getByRole('button')).toHaveClass('custom-class')
  })

  it('renders as child component when asChild is true', () => {
    render(
      <NPIButton asChild>
        <a href="/test">Link Button</a>
      </NPIButton>
    )
    
    const link = screen.getByRole('link')
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute('href', '/test')
  })
})

describe('NPIButton Accessibility', () => {
  it('has proper ARIA attributes', () => {
    render(
      <NPIButton aria-label="Custom label" aria-describedby="description">
        Button
      </NPIButton>
    )
    
    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-label', 'Custom label')
    expect(button).toHaveAttribute('aria-describedby', 'description')
  })

  it('supports keyboard navigation', () => {
    const handleClick = jest.fn()
    render(<NPIButton onClick={handleClick}>Keyboard Button</NPIButton>)
    
    const button = screen.getByRole('button')
    button.focus()
    expect(button).toHaveFocus()
    
    fireEvent.keyDown(button, { key: 'Enter' })
    expect(handleClick).toHaveBeenCalledTimes(1)
    
    fireEvent.keyDown(button, { key: ' ' })
    expect(handleClick).toHaveBeenCalledTimes(2)
  })
})
