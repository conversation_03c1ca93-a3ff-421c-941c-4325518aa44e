'use client'

import React, { useState, useEffect } from 'react'
import { ChevronUp } from 'lucide-react'
import { NPIButton } from '@/components/ui/npi-button'
import { cn } from '@/utilities/ui'

interface NPIScrollToTopProps {
  showAfter?: number
  className?: string
}

export const NPIScrollToTop: React.FC<NPIScrollToTopProps> = ({ showAfter = 300, className }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > showAfter) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener('scroll', toggleVisibility)
    return () => window.removeEventListener('scroll', toggleVisibility)
  }, [showAfter])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }

  if (!isVisible) {
    return null
  }

  return (
    <div className={cn('fixed bottom-6 right-6 z-50', className)}>
      <NPIButton
        onClick={scrollToTop}
        size="sm"
        variant="primary"
        className="rounded-full w-12 h-12 p-0 shadow-lg hover:shadow-xl transition-all duration-300"
        aria-label="Scroll to top"
      >
        <ChevronUp className="w-5 h-5" />
      </NPIButton>
    </div>
  )
}
