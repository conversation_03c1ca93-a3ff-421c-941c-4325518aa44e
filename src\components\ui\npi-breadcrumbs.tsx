import React from 'react'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/utilities/ui'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface NPIBreadcrumbsProps {
  items: BreadcrumbItem[]
  className?: string
}

export const NPIBreadcrumbs: React.FC<NPIBreadcrumbsProps> = ({ items, className }) => {
  return (
    <nav className={cn('flex items-center space-x-1 text-sm', className)} aria-label="Breadcrumb">
      <Link
        href="/"
        className="flex items-center text-muted-foreground hover:text-primary transition-colors font-npi"
      >
        <Home className="w-4 h-4" />
        <span className="sr-only">Home</span>
      </Link>

      {items.map((item, index) => (
        <React.Fragment key={index}>
          <ChevronRight className="w-4 h-4 text-muted-foreground" />
          {item.href && index < items.length - 1 ? (
            <Link
              href={item.href}
              className="text-muted-foreground hover:text-primary transition-colors font-npi"
            >
              {item.label}
            </Link>
          ) : (
            <span className="text-foreground font-medium font-npi">{item.label}</span>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}
