#!/bin/bash

# Script to update all existing users to have county ID 2
# Usage: ./scripts/update-users-county.sh

BASE_URL="http://localhost:3000/api"
TARGET_COUNTY_ID=2

# Admin credentials - update these with your actual credentials
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="01000010"

echo "🚀 Bulk User County Update Script"
echo "=================================="

# Step 1: Login to get auth token
echo "🔐 Logging in as admin..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/users/login" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$ADMIN_EMAIL\",\"password\":\"$ADMIN_PASSWORD\"}")

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.token // empty')

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ] || [ "$TOKEN" = "empty" ]; then
  echo "❌ Login failed. Please check your credentials."
  echo "Response: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ Login successful!"

# Step 2: Verify target county exists
echo "🔍 Verifying county $TARGET_COUNTY_ID exists..."
COUNTY_RESPONSE=$(curl -s "$BASE_URL/counties/$TARGET_COUNTY_ID")
COUNTY_NAME=$(echo "$COUNTY_RESPONSE" | jq -r '.name // .county.name // empty')

if [ -z "$COUNTY_NAME" ] || [ "$COUNTY_NAME" = "null" ] || [ "$COUNTY_NAME" = "empty" ]; then
  echo "❌ County $TARGET_COUNTY_ID not found!"
  echo "Response: $COUNTY_RESPONSE"
  exit 1
fi

echo "✅ Target county found: $COUNTY_NAME"

# Step 3: Get all users
echo "📋 Fetching all users..."
USERS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" "$BASE_URL/users?limit=1000")
TOTAL_USERS=$(echo "$USERS_RESPONSE" | jq '.totalDocs // 0')

if [ "$TOTAL_USERS" -eq 0 ]; then
  echo "❌ No users found!"
  exit 1
fi

echo "✅ Found $TOTAL_USERS users"

# Step 4: Get user IDs and current counties
echo "📊 Analyzing users..."
USER_DATA=$(echo "$USERS_RESPONSE" | jq -r '.docs[] | "\(.id)|\(.name)|\(.email)|\(.county // "null")"')

# Count users that need updating
USERS_TO_UPDATE=0
USERS_ALREADY_UPDATED=0

while IFS='|' read -r user_id name email current_county; do
  if [ "$current_county" != "$TARGET_COUNTY_ID" ]; then
    USERS_TO_UPDATE=$((USERS_TO_UPDATE + 1))
  else
    USERS_ALREADY_UPDATED=$((USERS_ALREADY_UPDATED + 1))
  fi
done <<< "$USER_DATA"

echo "📊 Analysis results:"
echo "  - Total users: $TOTAL_USERS"
echo "  - Users already in county $TARGET_COUNTY_ID: $USERS_ALREADY_UPDATED"
echo "  - Users to update: $USERS_TO_UPDATE"

if [ "$USERS_TO_UPDATE" -eq 0 ]; then
  echo "✅ All users already have county $TARGET_COUNTY_ID!"
  exit 0
fi

# Step 5: Show users to be updated
echo ""
echo "⚠️  Users to be updated to county $TARGET_COUNTY_ID ($COUNTY_NAME):"
echo "----------------------------------------"

PREVIEW_COUNT=0
while IFS='|' read -r user_id name email current_county; do
  if [ "$current_county" != "$TARGET_COUNTY_ID" ]; then
    if [ "$current_county" = "null" ]; then
      current_county_text="no county"
    else
      current_county_text="county $current_county"
    fi
    echo "  - $name ($email) - currently in $current_county_text"
    PREVIEW_COUNT=$((PREVIEW_COUNT + 1))
    
    # Show only first 10 for preview
    if [ "$PREVIEW_COUNT" -ge 10 ]; then
      REMAINING=$((USERS_TO_UPDATE - 10))
      if [ "$REMAINING" -gt 0 ]; then
        echo "  ... and $REMAINING more users"
      fi
      break
    fi
  fi
done <<< "$USER_DATA"

# Step 6: Confirmation
echo ""
read -p "🤔 Do you want to proceed with updating $USERS_TO_UPDATE users? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo "❌ Operation cancelled by user"
  exit 0
fi

# Step 7: Update users
echo "🚀 Starting bulk update..."
echo ""

SUCCESS_COUNT=0
ERROR_COUNT=0
CURRENT_USER=0

while IFS='|' read -r user_id name email current_county; do
  if [ "$current_county" != "$TARGET_COUNTY_ID" ]; then
    CURRENT_USER=$((CURRENT_USER + 1))
    echo "[$CURRENT_USER/$USERS_TO_UPDATE] Updating $name..."
    
    # Update user
    UPDATE_RESPONSE=$(curl -s -X PATCH "$BASE_URL/users/$user_id" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $TOKEN" \
      -d "{\"county\": $TARGET_COUNTY_ID}")
    
    # Check if update was successful
    if echo "$UPDATE_RESPONSE" | jq -e '.doc.id' > /dev/null 2>&1; then
      echo "  ✅ Success"
      SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
      ERROR_MESSAGE=$(echo "$UPDATE_RESPONSE" | jq -r '.error // .message // "Unknown error"')
      echo "  ❌ Failed: $ERROR_MESSAGE"
      ERROR_COUNT=$((ERROR_COUNT + 1))
    fi
    
    # Small delay to avoid overwhelming the server
    sleep 0.1
  fi
done <<< "$USER_DATA"

# Step 8: Summary
echo ""
echo "📊 Update Summary:"
echo "=================="
echo "✅ Successful updates: $SUCCESS_COUNT"
echo "❌ Failed updates: $ERROR_COUNT"

if [ "$ERROR_COUNT" -eq 0 ]; then
  echo ""
  echo "🎉 All users successfully updated to county $TARGET_COUNTY_ID ($COUNTY_NAME)!"
else
  echo ""
  echo "⚠️  Some updates failed. Please check the errors above."
fi

# Step 9: Verification
echo ""
echo "🔍 Verifying updates..."
VERIFICATION_RESPONSE=$(curl -s "$BASE_URL/counties/$TARGET_COUNTY_ID/users")
USERS_IN_COUNTY=$(echo "$VERIFICATION_RESPONSE" | jq '.totalUsers // 0')

echo "✅ County $TARGET_COUNTY_ID now has $USERS_IN_COUNTY users"

echo ""
echo "🏁 Script completed!"
