import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import { ResourcesHero } from '@/components/ResourcesHero'

export const metadata: Metadata = {
  title: 'Resources & Publications - Natural Products Industry Initiative',
  description:
    'Access comprehensive resources, research publications, policy documents, and educational materials supporting natural products development and traditional knowledge preservation in Kenya.',
}

const resourcesPageLayout = [
  {
    blockType: 'npiResourcesLibrary' as const,
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Knowledge Sharing Impact',
    variant: 'secondary',
  },
]

export default function ResourcesPage() {
  return (
    <article className="min-h-screen">
      <ResourcesHero />
      <main className="relative">
        <RenderBlocks blocks={resourcesPageLayout} />
      </main>
    </article>
  )
}
