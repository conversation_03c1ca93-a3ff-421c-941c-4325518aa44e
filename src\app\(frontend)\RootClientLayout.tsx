'use client'

import React, { useEffect } from 'react'
import { useHeaderTheme } from '@/providers/HeaderTheme'

export default function RootClientLayout({ children }: { children: React.ReactNode }) {
  const { setHeaderTheme } = useHeaderTheme()

  useEffect(() => {
    // Set initial theme
    setHeaderTheme('light')
  }, [setHeaderTheme])

  return <div className="relative min-h-screen flex flex-col">{children}</div>
}
