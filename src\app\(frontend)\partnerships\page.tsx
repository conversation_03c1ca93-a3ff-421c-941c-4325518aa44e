import React, { Fragment } from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'

// Import block components from organized structure
import {
  NPIPartnershipsHeroBlock,
  NPIPartnershipModelsBlock
} from '@/blocks/pages/partnerships/main'
import { NPIStatisticsBlock } from '@/blocks/shared'

const blockComponents = {
  npiPartnershipsHero: NPIPartnershipsHeroBlock,
  npiPartnershipModels: NPIPartnershipModelsBlock,
  npiStatistics: NPIStatisticsBlock,
}

export const metadata: Metadata = {
  title: 'Partnerships & Investment - Natural Products Industry Initiative',
  description:
    "Discover investment opportunities and partnership models in Kenya's natural products sector. Join us in creating sustainable economic opportunities while preserving traditional knowledge and empowering communities.",
}

const partnershipsPageLayout = [
  {
    blockType: 'npiPartnershipsHero' as const,
  },
  {
    blockType: 'npiPartnershipModels' as const,
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Partnership Impact',
    variant: 'secondary',
  },
]

export default function PartnershipsPage() {
  return (
    <>
      <PageClient />
      <article>
        {partnershipsPageLayout.map((block, index) => {
          const { blockType } = block

          if (blockType && blockType in blockComponents) {
            const Block = blockComponents[blockType]

            if (Block) {
              const isFirstBlock = index === 0

              return (
                <section
                  className={`
                    ${isFirstBlock ? '' : '-mt-1'}
                    relative
                    ${
                      index % 6 === 0
                        ? 'bg-[#E5E1DC]'
                        : index % 6 === 1
                          ? 'bg-[#8D8F78]/15'
                          : index % 6 === 2
                            ? 'bg-[#CABA9C]/20'
                            : index % 6 === 3
                              ? 'bg-[#4C6444]/12'
                              : index % 6 === 4
                                ? 'bg-[#2F2C29]/8'
                                : 'bg-[#CEC9BC]/25'
                    }
                  `}
                  key={index}
                >
                  <div className="w-full">
                    {/* @ts-expect-error there may be some mismatch between the expected types here */}
                    <Block {...block} disableInnerContainer />
                  </div>
                </section>
              )
            }
          }
          return null
        })}
      </article>
    </>
  )
}
