import type { Block } from 'payload'

export const NPISuccessStories: Block = {
  slug: 'npiSuccessStories',
  interfaceName: 'NPISuccessStoriesBlock',
  fields: [
    {
      name: 'title',
      type: 'text',
      defaultValue: 'Success Stories',
      label: 'Title',
    },
    {
      name: 'description',
      type: 'text',
      defaultValue: 'Real impact stories from communities across Kenya.',
      label: 'Description',
    },
  ],
  labels: {
    plural: 'NPI Success Stories Blocks',
    singular: 'NPI Success Stories Block',
  },
}
