'use client'

import React from 'react'
import { X } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/utilities/ui'

interface NPIModalProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

const sizeClasses = {
  sm: 'max-w-md',
  md: 'max-w-lg',
  lg: 'max-w-2xl',
  xl: 'max-w-4xl',
}

export const NPIModal: React.FC<NPIModalProps> = ({
  isOpen,
  onClose,
  children,
  className,
  size = 'md',
}) => {
  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
          />

          {/* Modal */}
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              className={cn(
                'relative w-full bg-white shadow-xl max-h-[90vh] flex flex-col',
                sizeClasses[size],
                className,
              )}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close button */}
              <button
                onClick={onClose}
                className="absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>

              {/* Content */}
              <div className="relative overflow-y-auto flex-1">{children}</div>
            </motion.div>
          </div>
        </>
      )}
    </AnimatePresence>
  )
}

interface TeamMember {
  id: string
  name: string
  title: string
  department?: string
  bio: string
  image: string
  email?: string
  phone?: string
  expertise: string[]
  achievements: string[]
  education: string[]
}

interface NPITeamModalProps {
  isOpen: boolean
  onClose: () => void
  member: TeamMember | null
}

export const NPITeamModal: React.FC<NPITeamModalProps> = ({ isOpen, onClose, member }) => {
  if (!member) return null

  return (
    <NPIModal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      className="bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC]"
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start gap-6 mb-6 bg-gradient-to-r from-[#CABA9C]/30 to-[#8A6240]/20 p-4 border border-[#8D8F78]/30">
          <div className="w-24 h-24 bg-gradient-to-br from-[#6E3C19] to-[#A7795E] flex items-center justify-center text-white text-2xl font-bold shadow-lg">
            {member.name
              .split(' ')
              .map((n) => n[0])
              .join('')}
          </div>
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-[#34170D] mb-1">{member.name}</h2>
            <p className="text-lg text-[#6E3C19] font-medium mb-2">{member.title}</p>
            {member.department && (
              <p className="text-[#46372A] mb-3 font-medium">{member.department}</p>
            )}
            <div className="flex flex-wrap gap-2">
              {member.expertise.slice(0, 3).map((skill, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-[#8A6240]/20 text-[#4D2D18] text-sm font-medium border border-[#8A6240]/30"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Bio */}
        <div className="mb-6 bg-gradient-to-r from-[#E5E1DC]/50 to-[#CEC9BC]/50 p-4 border border-[#8D8F78]/20">
          <h3 className="text-lg font-bold text-[#34170D] mb-3 flex items-center gap-2">
            <div className="w-3 h-3 bg-[#6E3C19]"></div>
            Biography
          </h3>
          <p className="text-[#46372A] leading-relaxed">{member.bio}</p>
        </div>

        {/* Expertise */}
        <div className="mb-6 bg-gradient-to-r from-[#CABA9C]/30 to-[#8A6240]/20 p-4 border border-[#8A6240]/30">
          <h3 className="text-lg font-bold text-[#34170D] mb-3 flex items-center gap-2">
            <div className="w-3 h-3 bg-[#A7795E]"></div>
            Areas of Expertise
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {member.expertise.map((skill, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="w-2 h-2 bg-[#6E3C19]"></div>
                <span className="text-[#46372A]">{skill}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Education */}
        {member.education.length > 0 && (
          <div className="mb-6 bg-gradient-to-r from-[#8D8F78]/20 to-[#4C6444]/20 p-4 border border-[#4C6444]/30">
            <h3 className="text-lg font-bold text-[#34170D] mb-3 flex items-center gap-2">
              <div className="w-3 h-3 bg-[#4C6444]"></div>
              Education
            </h3>
            <div className="space-y-2">
              {member.education.map((edu, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#102820]"></div>
                  <span className="text-[#46372A]">{edu}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Achievements */}
        {member.achievements.length > 0 && (
          <div className="mb-6 bg-gradient-to-r from-[#A7795E]/20 to-[#CABA9C]/30 p-4 border border-[#A7795E]/30">
            <h3 className="text-lg font-bold text-[#34170D] mb-3 flex items-center gap-2">
              <div className="w-3 h-3 bg-[#8A6240]"></div>
              Key Achievements
            </h3>
            <div className="space-y-2">
              {member.achievements.map((achievement, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#A7795E]"></div>
                  <span className="text-[#46372A]">{achievement}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Contact */}
        {(member.email || member.phone) && (
          <div className="border-t-2 border-[#6E3C19]/30 pt-4 bg-gradient-to-r from-[#E5E1DC]/50 to-[#CEC9BC]/50 p-4">
            <h3 className="text-lg font-bold text-[#34170D] mb-3 flex items-center gap-2">
              <div className="w-3 h-3 bg-[#6E3C19]"></div>
              Contact Information
            </h3>
            <div className="space-y-2">
              {member.email && (
                <p className="text-[#46372A]">
                  <span className="font-medium text-[#6E3C19]">Email:</span> {member.email}
                </p>
              )}
              {member.phone && (
                <p className="text-[#46372A]">
                  <span className="font-medium text-[#6E3C19]">Phone:</span> {member.phone}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </NPIModal>
  )
}

// Strategic Pillar Modal Types
interface Milestone {
  title: string
  status: 'completed' | 'in-progress' | 'planned'
  date: string
}

interface RelatedProject {
  title: string
  status: 'active' | 'completed' | 'upcoming'
  link: string
}

interface StrategicPillar {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  color: string
  objectives: string[]
  keyActivities: string[]
  milestones: Milestone[]
  relatedProjects: RelatedProject[]
}

interface NPIPillarModalProps {
  isOpen: boolean
  onClose: () => void
  pillar: StrategicPillar | null
}

export const NPIPillarModal: React.FC<NPIPillarModalProps> = ({ isOpen, onClose, pillar }) => {
  if (!pillar) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-[#4C6444]/20 text-[#102820] border border-[#4C6444]/30'
      case 'in-progress':
        return 'bg-[#8A6240]/20 text-[#4D2D18] border border-[#8A6240]/30'
      case 'planned':
        return 'bg-[#8D8F78]/20 text-[#46372A] border border-[#8D8F78]/30'
      case 'active':
        return 'bg-[#6E3C19]/20 text-[#34170D] border border-[#6E3C19]/30'
      case 'upcoming':
        return 'bg-[#A7795E]/20 text-[#4D2D18] border border-[#A7795E]/30'
      default:
        return 'bg-[#CEC9BC]/20 text-[#2F2C29] border border-[#CEC9BC]/30'
    }
  }

  return (
    <NPIModal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      className="bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC]"
    >
      <div className="p-6">
        {/* Header */}
        <div
          className={`flex items-start gap-6 mb-6 bg-gradient-to-r ${pillar.color} p-6 text-white`}
        >
          <div className="w-16 h-16 bg-white/20 flex items-center justify-center text-white shadow-lg">
            {pillar.icon}
          </div>
          <div className="flex-1">
            <h2 className="text-3xl font-bold text-white mb-2">{pillar.title}</h2>
            <p className="text-white/90 text-lg leading-relaxed">{pillar.description}</p>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Objectives */}
          <div className="bg-gradient-to-r from-[#E5E1DC]/50 to-[#CEC9BC]/50 p-4 border border-[#8D8F78]/20">
            <h3 className="text-lg font-bold text-[#34170D] mb-3 flex items-center gap-2">
              <div className="w-3 h-3 bg-[#6E3C19]"></div>
              Objectives
            </h3>
            <ul className="space-y-2">
              {pillar.objectives.map((objective, index) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-[#6E3C19] mt-2 flex-shrink-0"></div>
                  <span className="text-[#46372A]">{objective}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Key Activities */}
          <div className="bg-gradient-to-r from-[#CABA9C]/30 to-[#8A6240]/20 p-4 border border-[#8A6240]/30">
            <h3 className="text-lg font-bold text-[#34170D] mb-3 flex items-center gap-2">
              <div className="w-3 h-3 bg-[#A7795E]"></div>
              Key Activities
            </h3>
            <ul className="space-y-2">
              {pillar.keyActivities.map((activity, index) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-[#A7795E] mt-2 flex-shrink-0"></div>
                  <span className="text-[#46372A]">{activity}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Milestones */}
        <div className="mt-6 bg-gradient-to-r from-[#8D8F78]/20 to-[#4C6444]/20 p-4 border border-[#4C6444]/30">
          <h3 className="text-lg font-bold text-[#34170D] mb-4 flex items-center gap-2">
            <div className="w-3 h-3 bg-[#4C6444]"></div>
            Milestones & Progress
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            {pillar.milestones.map((milestone, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-white/50 border border-[#8D8F78]/20"
              >
                <div className="flex-1">
                  <h4 className="font-semibold text-[#34170D] text-sm">{milestone.title}</h4>
                  <p className="text-xs text-[#46372A]">{milestone.date}</p>
                </div>
                <span
                  className={`px-2 py-1 text-xs font-medium ${getStatusColor(milestone.status)}`}
                >
                  {milestone.status.replace('-', ' ')}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Related Projects */}
        <div className="mt-6 bg-gradient-to-r from-[#A7795E]/20 to-[#CABA9C]/30 p-4 border border-[#A7795E]/30">
          <h3 className="text-lg font-bold text-[#34170D] mb-4 flex items-center gap-2">
            <div className="w-3 h-3 bg-[#8A6240]"></div>
            Related Projects
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            {pillar.relatedProjects.map((project, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-white/50 border border-[#A7795E]/20"
              >
                <div className="flex-1">
                  <h4 className="font-semibold text-[#34170D] text-sm">{project.title}</h4>
                  <a
                    href={project.link}
                    className="text-xs text-[#6E3C19] hover:text-[#A7795E] transition-colors"
                  >
                    Learn More →
                  </a>
                </div>
                <span className={`px-2 py-1 text-xs font-medium ${getStatusColor(project.status)}`}>
                  {project.status}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </NPIModal>
  )
}

export type { TeamMember, StrategicPillar }
