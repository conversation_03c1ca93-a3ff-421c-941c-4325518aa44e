Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAF2130000 ntdll.dll
7FFAF0BF0000 KERNEL32.DLL
7FFAEF520000 KERNELBASE.dll
7FFAEFE70000 USER32.dll
7FFAEF8E0000 win32u.dll
7FFAF20C0000 GDI32.dll
7FFAEFA30000 gdi32full.dll
7FFAEFB50000 msvcp_win.dll
7FFAEF910000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAF17C0000 advapi32.dll
7FFAF1660000 msvcrt.dll
7FFAF0CC0000 sechost.dll
7FFAEF4F0000 bcrypt.dll
7FFAF0090000 RPCRT4.dll
7FFAEEB90000 CRYPTBASE.DLL
7FFAEF470000 bcryptPrimitives.dll
7FFAF1880000 IMM32.DLL
