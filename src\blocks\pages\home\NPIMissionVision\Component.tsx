'use client'

import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { motion } from 'framer-motion'
import { Target, Eye, Heart, Users, Lightbulb, Shield, Globe, Zap } from 'lucide-react'

interface MissionVisionItem {
  title: string
  content: string
  icon?: string
}

interface NPIMissionVisionProps {
  title?: string
  description?: string
  mission?: MissionVisionItem
  vision?: MissionVisionItem
  coreValues?: MissionVisionItem[]
  themes?: MissionVisionItem[]
}

export const NPIMissionVisionBlock: React.FC<NPIMissionVisionProps> = ({
  title = 'Our Mission, Vision & Core Values',
  description = 'Guiding principles that drive our commitment to sustainable development and natural products innovation.',
  mission = {
    title: 'Mission',
    content:
      "To harness Kenya's indigenous knowledge and natural resources for sustainable economic development, creating value-added products that benefit local communities while preserving traditional knowledge and environmental heritage.",
  },
  vision = {
    title: 'Vision',
    content:
      'A thriving natural products industry in Kenya that serves as a model for sustainable development, community empowerment, and indigenous knowledge preservation across Africa.',
  },
  coreValues = [
    {
      title: 'Sustainability',
      content: 'Environmental stewardship and long-term thinking in all our initiatives.',
    },
    {
      title: 'Community Empowerment',
      content:
        'Ensuring local communities are central to and benefit from natural product development.',
    },
    {
      title: 'Innovation',
      content: 'Combining traditional knowledge with modern science and technology.',
    },
    {
      title: 'Integrity',
      content: 'Ethical practices, transparency, and respect for indigenous knowledge rights.',
    },
  ],
  themes = [
    {
      title: 'Indigenous Knowledge Documentation',
      content: 'Systematic collection and preservation of traditional knowledge systems.',
    },
    {
      title: 'Product Development & Commercialization',
      content: 'Transforming traditional knowledge into market-ready products.',
    },
    {
      title: 'Capacity Building',
      content: 'Empowering communities with skills and resources for sustainable development.',
    },
    {
      title: 'Market Access & Investment',
      content: 'Creating pathways to local and international markets for natural products.',
    },
  ],
}) => {
  return (
    <NPISection
      size="tight"
      className="bg-gradient-to-br from-[#E5E1DC] via-[#CEC9BC] to-[#CABA9C] relative overflow-hidden"
    >
      {/* Enhanced Animated Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/3 right-1/4 w-80 h-80 bg-gradient-to-r from-[#A7795E]/20 to-[#4C6444]/15 blur-3xl"
          animate={{
            x: [0, -30, 0],
            y: [0, 20, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute bottom-1/3 left-1/4 w-96 h-96 bg-gradient-to-l from-[#6E3C19]/15 to-[#8A6240]/15 blur-3xl"
          animate={{
            x: [0, 40, 0],
            y: [0, -25, 0],
            scale: [1, 0.9, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 3,
          }}
        />
        {/* Additional floating elements */}
        <motion.div
          className="absolute top-20 left-1/2 w-32 h-32 bg-accent/10 rounded-full blur-2xl"
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 1,
          }}
        />
      </div>

      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="inline-flex items-center px-4 py-2 bg-[#4C6444]/20 backdrop-blur-md border-2 border-[#4C6444] text-[#34170D] text-sm font-medium mb-4 rounded-lg"
          >
            <span className="w-2 h-2 bg-[#4C6444] mr-2 animate-pulse rounded-full"></span>
            Our Foundation
          </motion.div>
          <NPISectionTitle className="text-[#34170D] leading-[1.1] tracking-[-0.02em]">
            {title}
          </NPISectionTitle>
          <NPISectionDescription className="font-light leading-[1.6] text-[#46372A]">
            {description}
          </NPISectionDescription>
        </NPISectionHeader>

        {/* Mission and Vision */}
        <div className="grid lg:grid-cols-2 gap-8 mb-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="relative"
          >
            <div className="bg-gradient-to-br from-[#A7795E]/20 to-[#8A6240]/10 backdrop-blur-sm p-8 border-2 border-[#A7795E]/30 shadow-lg hover:shadow-xl transition-all duration-300 group h-[360px] w-full flex flex-col">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-[#A7795E] to-[#8A6240] flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Target className="w-8 h-8 text-[#E5E1DC]" />
                </div>
                <h3 className="text-3xl font-bold text-[#34170D] group-hover:text-[#6E3C19] transition-colors">
                  {mission.title}
                </h3>
              </div>
              <p className="text-[#46372A] leading-[1.7] font-light text-lg flex-1">
                {mission.content}
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="bg-gradient-to-br from-[#4C6444]/20 to-[#102820]/10 backdrop-blur-sm p-8 border-2 border-[#4C6444]/30 shadow-lg hover:shadow-xl transition-all duration-300 group h-[360px] w-full flex flex-col">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-[#4C6444] to-[#102820] flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Eye className="w-8 h-8 text-[#E5E1DC]" />
                </div>
                <h3 className="text-3xl font-bold text-[#34170D] group-hover:text-[#6E3C19] transition-colors">
                  {vision.title}
                </h3>
              </div>
              <p className="text-[#46372A] leading-[1.7] font-light text-lg flex-1">
                {vision.content}
              </p>
            </div>
          </motion.div>
        </div>

        {/* Core Values */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-8"
        >
          <h3 className="text-3xl font-bold text-center mb-6 font-npi text-[#34170D]">
            Core Values
          </h3>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {coreValues.map((value, index) => {
              const icons = [Heart, Shield, Lightbulb, Globe]
              const IconComponent = icons[index % icons.length]

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC] border-2 border-[#8D8F78]/30 p-6 shadow-lg hover:shadow-xl transition-all duration-300 group hover:border-[#4C6444] h-[280px] w-full flex flex-col"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-[#6E3C19] to-[#34170D] flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <IconComponent className="w-6 h-6 text-[#E5E1DC]" />
                  </div>
                  <h4 className="text-xl text-[#34170D] font-semibold mb-3 group-hover:text-[#6E3C19] transition-colors">
                    {value.title}
                  </h4>
                  <p className="text-[#46372A] leading-[1.6] font-light flex-1">{value.content}</p>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Strategic Themes */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <h3 className="text-3xl font-bold text-center mb-6 font-npi text-[#34170D]">
            Strategic Themes
          </h3>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {themes.map((theme, index) => {
              const icons = [Target, Users, Lightbulb, Zap]
              const IconComponent = icons[index % icons.length]

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center bg-gradient-to-br from-[#CABA9C] to-[#8A6240]/20 border-2 border-[#A7795E]/30 p-6 shadow-lg hover:shadow-xl transition-all duration-300 group hover:border-[#6E3C19] h-[280px] w-full flex flex-col"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-[#8A6240] to-[#4D2D18] flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <IconComponent className="w-6 h-6 text-[#E5E1DC]" />
                  </div>
                  <h4 className="text-xl text-[#34170D] font-semibold mb-3 group-hover:text-[#6E3C19] transition-colors">
                    {theme.title}
                  </h4>
                  <p className="text-[#46372A] leading-[1.6] font-light flex-1">{theme.content}</p>
                </motion.div>
              )
            })}
          </div>
        </motion.div>
      </div>
    </NPISection>
  )
}
