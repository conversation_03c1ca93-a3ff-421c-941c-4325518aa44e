'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { NPIParallaxHero } from '@/components/ui/npi-parallax-hero'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { Users, Globe, Handshake, ArrowDown } from 'lucide-react'

interface NPIPartnersHeroProps {
  title?: string
  subtitle?: string
  totalPartners?: number
  countries?: number
  sectors?: number
}

export const NPIPartnersHeroBlock: React.FC<NPIPartnersHeroProps> = ({
  title = 'Our Partners',
  totalPartners = 25,
  countries = 8,
  sectors = 12,
}) => {
  return (
    <NPIParallaxHero
      backgroundVideo="/assets/video 2.mp4"
      height="large"
      parallaxSpeed={0.3}
      overlayOpacity={0.7}
      className="relative min-h-[70vh] max-h-[85vh] -mt-16 pt-16"
    >
      {/* Custom Layout Container - Absolute positioning for extreme corners */}
      <div className="absolute inset-0 text-white z-20">
        {/* Top Left Section - Aligned with navbar container */}
        <motion.div
          className="absolute top-0 left-0 pt- px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Organization info */}
            <motion.p
              className="text-sm md:text-base text-[#E5E1DC]/90 font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Natural Products Industry Initiative
            </motion.p>

            {/* Main title */}
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#E5E1DC] leading-tight font-npi"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              {title}
            </motion.h1>

            {/* Subtitle */}
          </div>
        </motion.div>

        {/* Bottom Right Section - Statistics and Actions */}
        <motion.div
          className="absolute bottom-0 right-0 pb-4 px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Key Statistics */}
            <motion.div
              className="grid grid-cols-3 gap-4 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              <div className="bg-[#34170D]/30 backdrop-blur-md border border-[#A7795E]/40 p-4 text-center">
                <div className="text-2xl font-bold text-[#E5E1DC] mb-1 font-npi">
                  {totalPartners}+
                </div>
                <div className="text-[#CEC9BC]/80 text-sm font-npi">Partners</div>
              </div>
              <div className="bg-[#34170D]/30 backdrop-blur-md border border-[#A7795E]/40 p-4 text-center">
                <div className="text-2xl font-bold text-[#E5E1DC] mb-1 font-npi">{countries}</div>
                <div className="text-[#CEC9BC]/80 text-sm font-npi">Countries</div>
              </div>
              <div className="bg-[#34170D]/30 backdrop-blur-md border border-[#A7795E]/40 p-4 text-center">
                <div className="text-2xl font-bold text-[#E5E1DC] mb-1 font-npi">{sectors}</div>
                <div className="text-[#CEC9BC]/80 text-sm font-npi">Sectors</div>
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.4 }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-2 border-[#A7795E] bg-gradient-to-r from-[#CABA9C]/30 to-[#8A6240]/25 text-[#E5E1DC] hover:bg-gradient-to-r hover:from-[#A7795E] hover:to-[#CABA9C] hover:text-[#141311] backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold hover:border-[#CABA9C]"
                >
                  <Link href="#partners-showcase" className="flex items-center gap-2">
                    <ArrowDown className="w-5 h-5" />
                    Meet Our Partners
                  </Link>
                </NPIButton>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-2 border-[#8A6240] bg-gradient-to-r from-[#6E3C19]/30 to-[#34170D]/25 text-[#E5E1DC] hover:bg-gradient-to-r hover:from-[#8A6240] hover:to-[#A7795E] hover:text-[#141311] backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold hover:border-[#A7795E]"
                >
                  <Link href="/partnerships/apply" className="flex items-center gap-2">
                    <Handshake className="w-5 h-5" />
                    Become a Partner
                  </Link>
                </NPIButton>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </NPIParallaxHero>
  )
}
