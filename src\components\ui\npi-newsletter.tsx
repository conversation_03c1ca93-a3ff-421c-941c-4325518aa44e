'use client'

import React, { useState } from 'react'
import { NPI<PERSON>ard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { Mail, CheckCircle, AlertCircle } from 'lucide-react'
import { cn } from '@/utilities/ui'

interface NPINewsletterProps {
  title?: string
  description?: string
  className?: string
}

export const NPINewsletter: React.FC<NPINewsletterProps> = ({
  title = 'Stay Updated',
  description = 'Subscribe to our newsletter for the latest updates on natural products development and traditional knowledge preservation.',
  className,
}) => {
  const [email, setEmail] = useState('')
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setStatus('loading')

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    if (email && email.includes('@')) {
      setStatus('success')
      setMessage('Thank you for subscribing! Check your email for confirmation.')
      setEmail('')
    } else {
      setStatus('error')
      setMessage('Please enter a valid email address.')
    }

    setTimeout(() => {
      setStatus('idle')
      setMessage('')
    }, 5000)
  }

  return (
    <NPICard
      className={cn(
        'bg-gradient-to-r from-primary/5 to-secondary/5 border-2 border-primary/20',
        className,
      )}
    >
      <NPICardHeader>
        <NPICardTitle className="text-center flex items-center justify-center gap-2">
          <Mail className="w-5 h-5 text-primary" />
          {title}
        </NPICardTitle>
      </NPICardHeader>
      <NPICardContent>
        <p className="text-center text-muted-foreground mb-6 font-npi">{description}</p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex gap-2">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address"
              className="flex-1 px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
              disabled={status === 'loading'}
              required
            />
            <NPIButton
              type="submit"
              variant="primary"
              disabled={status === 'loading'}
              className="px-6"
            >
              {status === 'loading' ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                'Subscribe'
              )}
            </NPIButton>
          </div>

          {message && (
            <div
              className={cn(
                'flex items-center gap-2 p-3 rounded-lg text-sm font-npi',
                status === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800',
              )}
            >
              {status === 'success' ? (
                <CheckCircle className="w-4 h-4" />
              ) : (
                <AlertCircle className="w-4 h-4" />
              )}
              {message}
            </div>
          )}
        </form>

        <p className="text-xs text-muted-foreground text-center mt-4 font-npi">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </NPICardContent>
    </NPICard>
  )
}
