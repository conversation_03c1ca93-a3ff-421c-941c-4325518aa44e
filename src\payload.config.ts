// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { nodemailerAdapter } from '@payloadcms/email-nodemailer'

import sharp from 'sharp' // sharp-import
import path from 'path'
import { buildConfig, PayloadRequest, createLocalReq } from 'payload'
import { fileURLToPath } from 'url'

import { Categories } from './collections/Categories'
import { Media } from './collections/Media'
import { Pages } from './collections/Pages'
import { Posts } from './collections/Posts'
import { Users } from './collections/Users'
import { Footer } from './Footer/config'
import { Header } from './Header/config'
import { plugins } from './plugins'
import { vercelBlobStorage } from '@payloadcms/storage-vercel-blob'
import { defaultLexical } from '@/fields/defaultLexical'
import { getServerSideURL } from './utilities/getURL'
import Events from './collections/Events'
import Speakers from './collections/Speakers'
import Counties from './collections/Counties'
import { eventsHandler } from './endpoints/events'
import {
  countiesHandler,
  countyByIdHandler,
  countiesInBoundsHandler,
  createCountyHandler,
  updateCountyHandler,
  deleteCountyHandler,
  countyUsersHandler,
} from './endpoints/counties'
import { seed } from './endpoints/seed'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  onInit: async (payload) => {
    // Check if database is empty and seed if needed
    try {
      const users = await payload.find({
        collection: 'users',
        limit: 1,
        pagination: false,
      })

      if (users.docs.length === 0) {
        payload.logger.info('Database appears to be empty. Starting seeding process...')

        // Create a local request object for seeding
        const req = await createLocalReq({}, payload)

        await seed({ payload, req })
        payload.logger.info('Database seeding completed successfully!')
      } else {
        payload.logger.info('Database already contains data. Skipping seeding.')
      }
    } catch (error) {
      payload.logger.error('Error during database seeding:', error)
    }
  },
  email: process.env.SMTP_HOST
    ? nodemailerAdapter({
        defaultFromAddress: process.env.FROM_EMAIL || '<EMAIL>',
        defaultFromName: process.env.FROM_NAME || 'Your App',
        transportOptions: {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587', 10),
          secure: process.env.SMTP_SECURE === 'true', // true for 465, false for 587
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
          },
        },
      })
    : undefined,
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below.
      beforeLogin: ['@/components/BeforeLogin'],
      // The `BeforeDashboard` component renders the 'welcome' block that you see after logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below.
      beforeDashboard: ['@/components/BeforeDashboard'],
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
  }),
  collections: [Pages, Posts, Media, Categories, Users, Events, Speakers, Counties],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [Header, Footer],
  endpoints: [
    {
      path: '/events',
      method: 'get',
      handler: eventsHandler as any,
    },
    // Counties CRUD endpoints
    {
      path: '/counties',
      method: 'get',
      handler: countiesHandler as any,
    },
    {
      path: '/counties',
      method: 'post',
      handler: createCountyHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'get',
      handler: countyByIdHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'put',
      handler: updateCountyHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'delete',
      handler: deleteCountyHandler as any,
    },
    {
      path: '/counties/bounds',
      method: 'get',
      handler: countiesInBoundsHandler as any,
    },
    {
      path: '/counties/:id/users',
      method: 'get',
      handler: countyUsersHandler as any,
    },
  ],
  plugins: [
    ...plugins,
    // Vercel Blob Storage for media uploads in production
    ...(process.env.BLOB_READ_WRITE_TOKEN
      ? [
          vercelBlobStorage({
            collections: {
              media: true,
            },
            token: process.env.BLOB_READ_WRITE_TOKEN,
          }),
        ]
      : []),
  ],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow logged in users to execute this endpoint (default)
        if (req.user) return true

        // If there is no logged in user, then check
        // for the Vercel Cron secret to be present as an
        // Authorization header:
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      },
    },
    tasks: [],
  },
})
