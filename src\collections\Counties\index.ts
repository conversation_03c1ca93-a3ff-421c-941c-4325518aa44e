import type { CollectionConfig } from 'payload'
import { anyone } from '../../access/anyone'
import { authenticated } from '../../access/authenticated'

export const Counties: CollectionConfig = {
  slug: 'counties',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone, // Public access for reading counties
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'isActive'],
    group: 'Geography',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'County Name',
      admin: {
        description: 'Full name of the county',
      },
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      label: 'County Code',
      admin: {
        description: 'Unique identifier code for the county (e.g., KE-001)',
      },
    },
    {
      name: 'coordinates',
      type: 'group',
      label: 'Geographic Coordinates',
      fields: [
        {
          name: 'latitude',
          type: 'number',
          required: true,
          label: 'Latitude',
          admin: {
            description: 'Latitude coordinate (decimal degrees)',
            step: 0.000001,
          },
          validate: (val: number | null | undefined) => {
            if (val !== null && val !== undefined && (val < -90 || val > 90)) {
              return 'Latitude must be between -90 and 90 degrees'
            }
            return true
          },
        },
        {
          name: 'longitude',
          type: 'number',
          required: true,
          label: 'Longitude',
          admin: {
            description: 'Longitude coordinate (decimal degrees)',
            step: 0.000001,
          },
          validate: (val: number | null | undefined) => {
            if (val !== null && val !== undefined && (val < -180 || val > 180)) {
              return 'Longitude must be between -180 and 180 degrees'
            }
            return true
          },
        },
      ],
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      admin: {
        description: 'General description of the county',
        rows: 4,
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
      admin: {
        description: 'Whether this county is currently active in the system',
      },
    },
  ],
  timestamps: true,
}

export default Counties
