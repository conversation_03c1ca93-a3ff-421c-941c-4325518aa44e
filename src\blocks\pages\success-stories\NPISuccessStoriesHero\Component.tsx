import React from 'react'
import Image from 'next/image'

interface NPISuccessStoriesHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPISuccessStoriesHeroBlock: React.FC<NPISuccessStoriesHeroProps> = ({
  title = 'Success Stories',
  backgroundImage = '/assets/product 1.jpg',
}) => {
  return (
    <section className="min-h-screen relative overflow-hidden -mt-16 pt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full z-0 bg-red-500">
        <Image
          src={backgroundImage}
          alt="Hero background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with bright color variants */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#102820]/30 via-[#4C6444]/50 to-[#2F2C29]/70 z-10" />

      {/* Top Left Title */}
      <div className="absolute top-24 left-8 z-30">
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-4">{title}</h1>
        <div className="w-24 h-1 bg-[#CEC9BC]" />
      </div>

      {/* Bottom Right Feature Card with bright colors */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#CEC9BC]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[#8D8F78]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-[#34170D]">Inspiring Stories</h3>
          <p className="text-sm sm:text-base mb-4 text-[#46372A]">
            Discover real impact stories of transformation, empowerment, and sustainable development
            across Kenya&apos;s communities.
          </p>
          <a
            href="#success-stories"
            className="text-[#6E3C19] hover:text-[#A7795E] text-sm font-medium transition-colors"
          >
            Explore Success Stories &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
