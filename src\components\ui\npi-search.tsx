'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Search, X, ArrowRight } from 'lucide-react'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { cn } from '@/utilities/ui'

interface SearchResult {
  id: string
  title: string
  description: string
  url: string
  type: 'page' | 'article' | 'resource' | 'program' | 'story'
}

interface NPISearchProps {
  className?: string
  placeholder?: string
}

export const NPISearch: React.FC<NPISearchProps> = ({
  className,
  placeholder = 'Search NPI...',
}) => {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)

  // Mock search results
  const mockResults: SearchResult[] = [
    {
      id: '1',
      title: 'About NPI',
      description: 'Learn about the Natural Products Industry Initiative and our mission.',
      url: '/about',
      type: 'page',
    },

    {
      id: '3',
      title: 'Strategic Pillars',
      description: 'Explore our four strategic pillars for natural products development.',
      url: '/strategic-pillars',
      type: 'page',
    },
    {
      id: '4',
      title: 'Aloe Vera Cooperative Success',
      description: "How a women's cooperative transformed their community through aloe vera.",
      url: '/success-stories/aloe-cooperative-baringo',
      type: 'story',
    },
    {
      id: '5',
      title: 'Traditional Medicine Documentation Guide',
      description: 'Step-by-step guide for documenting traditional medicine practices.',
      url: '/resources/tk-documentation-guide.pdf',
      type: 'resource',
    },
  ]

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setIsLoading(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 300))

    const filteredResults = mockResults.filter(
      (result) =>
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.description.toLowerCase().includes(searchQuery.toLowerCase()),
    )

    setResults(filteredResults)
    setIsLoading(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    setIsOpen(true)
    handleSearch(value)
  }

  const handleClear = () => {
    setQuery('')
    setResults([])
    setIsOpen(false)
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'page':
        return 'bg-blue-100 text-blue-800'
      case 'article':
        return 'bg-green-100 text-green-800'
      case 'resource':
        return 'bg-purple-100 text-purple-800'
      case 'program':
        return 'bg-orange-100 text-orange-800'
      case 'story':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div ref={searchRef} className={cn('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
        <input
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="w-full pl-10 pr-10 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
        />
        {query && (
          <button
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
          >
            <X className="w-5 h-5" />
          </button>
        )}
      </div>

      {isOpen && (query || results.length > 0) && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-border rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center">
              <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground font-npi">Searching...</p>
            </div>
          ) : results.length > 0 ? (
            <div className="py-2">
              {results.map((result) => (
                <Link
                  key={result.id}
                  href={result.url}
                  onClick={() => setIsOpen(false)}
                  className="block px-4 py-3 hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-foreground font-npi">{result.title}</h4>
                        <span
                          className={cn(
                            'px-2 py-0.5 rounded-full text-xs font-medium',
                            getTypeColor(result.type),
                          )}
                        >
                          {result.type}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground font-npi">{result.description}</p>
                    </div>
                    <ArrowRight className="w-4 h-4 text-muted-foreground flex-shrink-0 mt-1" />
                  </div>
                </Link>
              ))}

              {query && (
                <div className="border-t border-border px-4 py-3">
                  <NPIButton asChild variant="outline" size="sm" className="w-full">
                    <Link
                      href={`/search?q=${encodeURIComponent(query)}`}
                      onClick={() => setIsOpen(false)}
                    >
                      View all results for &ldquo;{query}&rdquo;
                    </Link>
                  </NPIButton>
                </div>
              )}
            </div>
          ) : query ? (
            <div className="p-4 text-center">
              <p className="text-sm text-muted-foreground font-npi">
                No results found for &ldquo;{query}&rdquo;
              </p>
              <NPIButton asChild variant="outline" size="sm" className="mt-2">
                <Link href="/contact" onClick={() => setIsOpen(false)}>
                  Contact us for help
                </Link>
              </NPIButton>
            </div>
          ) : (
            <div className="p-4">
              <p className="text-sm text-muted-foreground mb-3 font-npi">Popular searches:</p>
              <div className="flex flex-wrap gap-2">
                {['Programs', 'Success Stories', 'Partnerships', 'About'].map((term) => (
                  <button
                    key={term}
                    onClick={() => {
                      setQuery(term)
                      handleSearch(term)
                    }}
                    className="px-3 py-1 bg-primary/10 text-primary rounded-full text-xs hover:bg-primary/20 transition-colors font-npi"
                  >
                    {term}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
