import { withPayload } from '@payloadcms/next/withPayload'

import redirects from './redirects.js'

const NEXT_PUBLIC_SERVER_URL = process.env.VERCEL_PROJECT_PRODUCTION_URL
  ? `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  : undefined || process.env.__NEXT_PRIVATE_ORIGIN || 'http://localhost:3000'

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      // This block handles your main server URL, which should cover production and potentially local via NEXT_PUBLIC_SERVER_URL
      ...[NEXT_PUBLIC_SERVER_URL /* 'https://example.com' */]
        .map((item) => {
          if (!item) return null // Handle undefined case
          const url = new URL(item)

          return {
            hostname: url.hostname,
            protocol: url.protocol.replace(':', ''),
            port: url.port || '', // Include port if available
          }
        })
        .filter(Boolean), // Filter out null entries

      // Explicitly allow localhost for local development
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000', // Assuming Payload/Next.js runs on 3000 locally
      },
      {
        protocol: 'http',
        hostname: 'http://127.0.0.1', // Another common local IP
        port: '4040',
      },
      // Your existing local development IP address
      {
        protocol: 'http',
        hostname: '***************',
        port: '3000',
      },
      // If Payload CMS is running on a different port locally (e.g., 8000 for its API)
      // and serving images from there directly, you might need to add:
      // {
      //   protocol: 'http',
      //   hostname: 'localhost',
      //   port: '8000', // Or whatever port Payload's API runs on
      // },
      // {
      //   protocol: 'http',
      //   hostname: '127.0.0.1',
      //   port: '8000',
      // },
      // {
      //   protocol: 'http',
      //   hostname: '***************',
      //   port: '8000',
      // },

      // External image sources for NPI website (keep these)
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
    ],
  },
  webpack: (webpackConfig) => {
    webpackConfig.resolve.extensionAlias = {
      '.cjs': ['.cts', '.cjs'],
      '.js': ['.ts', '.tsx', '.js', '.jsx'],
      '.mjs': ['.mts', '.mjs'],
    }

    return webpackConfig
  },
  reactStrictMode: true,
  redirects,
  typescript: {
    // ignore TypeScript errors on build
    ignoreBuildErrors: true,
  },
  eslint: {
    // ignore ESLint errors on build
    ignoreDuringBuilds: true,
  },
}

export default withPayload(nextConfig, { devBundleServerPackages: false })
