import * as React from 'react'
import { cn } from '@/utilities/ui'

const NPICard = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        'border border-border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200',
        className,
      )}
      {...props}
    />
  ),
)
NPICard.displayName = 'NPICard'

const NPICardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />
  ),
)
NPICardHeader.displayName = 'NPICardHeader'

const NPICardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn('text-2xl font-semibold leading-none tracking-tight font-npi', className)}
    {...props}
  />
))
NPICardTitle.displayName = 'NPICardTitle'

const NPICardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn('text-sm text-muted-foreground font-npi', className)} {...props} />
))
NPICardDescription.displayName = 'NPICardDescription'

const NPICardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />
  ),
)
NPICardContent.displayName = 'NPICardContent'

const NPICardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />
  ),
)
NPICardFooter.displayName = 'NPICardFooter'

export { NPICard, NPICardHeader, NPICardFooter, NPICardTitle, NPICardDescription, NPICardContent }
