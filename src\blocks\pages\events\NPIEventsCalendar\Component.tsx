'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPI<PERSON>ard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Tag, ExternalLink, Filter } from 'lucide-react'

interface Event {
  id: string
  title: string
  description: string
  date: string
  endDate?: string
  time: string
  location: string
  venue: string
  type: 'conference' | 'workshop' | 'webinar' | 'training' | 'exhibition' | 'meeting'
  category: string
  capacity: number
  registered: number
  price: string
  organizer: string
  tags: string[]
  registrationUrl?: string
  featured?: boolean
}

interface NPIEventsCalendarProps {
  title?: string
  description?: string
  events?: Event[]
}

export const NPIEventsCalendarBlock: React.FC<NPIEventsCalendarProps> = ({
  title = 'Upcoming Events',
  description = 'Join us at conferences, workshops, and training sessions designed to advance natural products development and share knowledge across Kenya.',
  events = [
    {
      id: 'natural-products-conference-2024',
      title: 'Kenya Natural Products Conference 2024',
      description:
        "Annual conference bringing together researchers, entrepreneurs, and policymakers to discuss the future of Kenya's natural products sector.",
      date: '2024-03-15',
      endDate: '2024-03-17',
      time: '9:00 AM - 5:00 PM',
      location: 'Nairobi',
      venue: 'Kenyatta International Conference Centre',
      type: 'conference',
      category: 'Industry Conference',
      capacity: 500,
      registered: 320,
      price: 'KES 15,000',
      organizer: 'NPI & Partners',
      tags: ['conference', 'research', 'networking', 'policy'],
      registrationUrl: '/events/register/natural-products-conference-2024',
      featured: true,
    },
    {
      id: 'traditional-medicine-workshop',
      title: 'Traditional Medicine Documentation Workshop',
      description:
        'Hands-on workshop for community health workers on documenting traditional medicine practices and ensuring ethical protocols.',
      date: '2024-02-20',
      time: '8:00 AM - 4:00 PM',
      location: 'Meru',
      venue: 'Meru University of Science & Technology',
      type: 'workshop',
      category: 'Capacity Building',
      capacity: 50,
      registered: 35,
      price: 'Free',
      organizer: 'NPI Training Unit',
      tags: ['workshop', 'traditional medicine', 'documentation', 'ethics'],
      registrationUrl: '/events/register/traditional-medicine-workshop',
      featured: false,
    },
    {
      id: 'ip-protection-webinar',
      title: 'Intellectual Property Protection for Traditional Knowledge',
      description:
        'Online webinar covering legal frameworks and practical steps for protecting traditional knowledge intellectual property.',
      date: '2024-02-10',
      time: '2:00 PM - 4:00 PM',
      location: 'Online',
      venue: 'Zoom Platform',
      type: 'webinar',
      category: 'Legal & IP',
      capacity: 200,
      registered: 145,
      price: 'Free',
      organizer: 'NPI Legal Team',
      tags: ['webinar', 'intellectual property', 'legal', 'online'],
      registrationUrl: '/events/register/ip-protection-webinar',
      featured: false,
    },
    {
      id: 'youth-entrepreneurship-training',
      title: 'Youth Entrepreneurship in Natural Products',
      description:
        'Three-day intensive training program for young entrepreneurs interested in starting natural products businesses.',
      date: '2024-02-25',
      endDate: '2024-02-27',
      time: '9:00 AM - 5:00 PM',
      location: 'Kisumu',
      venue: 'Maseno University',
      type: 'training',
      category: 'Youth Development',
      capacity: 80,
      registered: 65,
      price: 'KES 5,000',
      organizer: 'NPI Youth Program',
      tags: ['training', 'youth', 'entrepreneurship', 'business'],
      registrationUrl: '/events/register/youth-entrepreneurship-training',
      featured: true,
    },
    {
      id: 'community-leaders-meeting',
      title: 'Community Leaders Quarterly Meeting',
      description:
        'Quarterly meeting with community leaders to discuss program progress, challenges, and upcoming initiatives.',
      date: '2024-02-05',
      time: '10:00 AM - 3:00 PM',
      location: 'Nakuru',
      venue: 'Nakuru County Assembly Hall',
      type: 'meeting',
      category: 'Community Engagement',
      capacity: 100,
      registered: 75,
      price: 'Free',
      organizer: 'NPI Community Unit',
      tags: ['meeting', 'community leaders', 'quarterly', 'engagement'],
      featured: false,
    },
    {
      id: 'natural-products-exhibition',
      title: 'Kenya Natural Products Exhibition',
      description:
        'Exhibition showcasing innovative natural products from communities across Kenya, featuring live demonstrations and networking.',
      date: '2024-04-10',
      endDate: '2024-04-12',
      time: '10:00 AM - 6:00 PM',
      location: 'Mombasa',
      venue: 'Mombasa Convention Centre',
      type: 'exhibition',
      category: 'Product Showcase',
      capacity: 1000,
      registered: 450,
      price: 'KES 2,000',
      organizer: 'NPI & Export Promotion Council',
      tags: ['exhibition', 'products', 'showcase', 'networking'],
      registrationUrl: '/events/register/natural-products-exhibition',
      featured: true,
    },
    {
      id: 'research-methodology-workshop',
      title: 'Research Methodology for Traditional Knowledge Studies',
      description:
        'Workshop for researchers on appropriate methodologies for studying traditional knowledge systems.',
      date: '2024-03-05',
      time: '9:00 AM - 5:00 PM',
      location: 'Eldoret',
      venue: 'Moi University',
      type: 'workshop',
      category: 'Research Training',
      capacity: 40,
      registered: 28,
      price: 'KES 3,000',
      organizer: 'NPI Research Unit',
      tags: ['workshop', 'research', 'methodology', 'academic'],
      registrationUrl: '/events/register/research-methodology-workshop',
      featured: false,
    },
    {
      id: 'sustainability-practices-webinar',
      title: 'Sustainable Harvesting Practices Webinar',
      description:
        'Online session covering best practices for sustainable harvesting of natural resources and environmental conservation.',
      date: '2024-02-15',
      time: '3:00 PM - 5:00 PM',
      location: 'Online',
      venue: 'Microsoft Teams',
      type: 'webinar',
      category: 'Environmental Conservation',
      capacity: 150,
      registered: 95,
      price: 'Free',
      organizer: 'NPI Environmental Unit',
      tags: ['webinar', 'sustainability', 'harvesting', 'conservation'],
      registrationUrl: '/events/register/sustainability-practices-webinar',
      featured: false,
    },
  ],
}) => {
  const [selectedType, setSelectedType] = useState('All Types')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')

  const types = ['All Types', ...Array.from(new Set(events.map((e) => e.type)))]
  const categories = ['All Categories', ...Array.from(new Set(events.map((e) => e.category)))]

  const filteredEvents = events.filter((event) => {
    return (
      (selectedType === 'All Types' || event.type === selectedType) &&
      (selectedCategory === 'All Categories' || event.category === selectedCategory)
    )
  })

  const featuredEvents = filteredEvents.filter((e) => e.featured)
  const regularEvents = filteredEvents.filter((e) => !e.featured)

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'conference':
        return 'bg-[#A7795E] text-[#E5E1DC]'
      case 'workshop':
        return 'bg-[#8A6240] text-[#E5E1DC]'
      case 'webinar':
        return 'bg-[#6E3C19] text-[#E5E1DC]'
      case 'training':
        return 'bg-[#4D2D18] text-[#E5E1DC]'
      case 'exhibition':
        return 'bg-[#8D8F78] text-[#E5E1DC]'
      case 'meeting':
        return 'bg-[#CABA9C] text-[#2F2C29]'
      default:
        return 'bg-[#CEC9BC] text-[#2F2C29]'
    }
  }

  const formatDate = (dateString: string, endDateString?: string) => {
    const startDate = new Date(dateString)
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }

    if (endDateString) {
      const endDate = new Date(endDateString)
      if (startDate.getMonth() === endDate.getMonth()) {
        return `${startDate.getDate()}-${endDate.getDate()} ${startDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`
      } else {
        return `${startDate.toLocaleDateString('en-US', options)} - ${endDate.toLocaleDateString('en-US', options)}`
      }
    }

    return startDate.toLocaleDateString('en-US', options)
  }

  return (
    <NPISection id="events">
      <NPISectionHeader>
        <NPISectionTitle className="text-[#2F2C29]">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#46372A]">{description}</NPISectionDescription>
        <div className="w-24 h-1 bg-gradient-to-r from-[#A7795E] via-[#8A6240] to-[#6E3C19] mx-auto mt-4"></div>
      </NPISectionHeader>

      {/* Filters */}
      <NPICard className="mb-8 rounded-none border-l-4 border-[#A7795E]">
        <NPICardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-[#6E3C19]" />
            <span className="font-medium font-npi text-[#2F2C29]">Filter Events:</span>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-[#46372A]">
                Event Type
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full p-2 border border-[#CEC9BC] focus:outline-none focus:ring-2 focus:ring-[#A7795E] font-npi bg-[#E5E1DC] text-[#2F2C29]"
              >
                {types.map((type) => (
                  <option key={type} value={type}>
                    {type === 'All Types' ? type : type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-[#46372A]">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-[#CEC9BC] focus:outline-none focus:ring-2 focus:ring-[#A7795E] font-npi bg-[#E5E1DC] text-[#2F2C29]"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Featured Events */}
      {featuredEvents.length > 0 && (
        <div className="mb-12">
          <div className="flex items-center gap-4 mb-6">
            <h3 className="text-2xl font-bold font-npi text-[#2F2C29]">Featured Events</h3>
            <div className="flex-1 h-0.5 bg-gradient-to-r from-[#A7795E] to-transparent"></div>
          </div>
          <div className="grid lg:grid-cols-4 gap-6">
            {featuredEvents.slice(0, 4).map((event) => (
              <NPICard
                key={event.id}
                className="overflow-hidden hover:shadow-xl transition-all duration-300 rounded-none"
              >
                <NPICardHeader>
                  <div className="flex items-center justify-between mb-3">
                    <span className={`px-3 py-1 text-sm font-medium ${getTypeColor(event.type)}`}>
                      {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                    </span>
                    <span className="bg-[#34170D] text-[#E5E1DC] px-2 py-1 text-xs font-medium">
                      Featured
                    </span>
                  </div>
                  <NPICardTitle className="text-xl leading-tight">{event.title}</NPICardTitle>
                  <div className="text-sm text-primary font-medium font-npi">{event.category}</div>
                </NPICardHeader>

                <NPICardContent>
                  <p className="text-muted-foreground leading-relaxed mb-4 font-npi">
                    {event.description}
                  </p>

                  <div className="space-y-2 text-sm mb-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="font-npi">{formatDate(event.date, event.endDate)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="font-npi">{event.time}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="font-npi">
                        {event.venue}, {event.location}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span className="font-npi">
                        {event.registered}/{event.capacity} registered
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <span className="font-semibold text-lg text-primary font-npi">
                      {event.price}
                    </span>
                    <div className="flex flex-wrap gap-1">
                      {event.tags.slice(0, 3).map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-primary/10 text-primary text-xs rounded font-npi"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </NPICardContent>

                <NPICardFooter className="flex gap-2">
                  {event.registrationUrl ? (
                    <NPIButton
                      asChild
                      variant="primary"
                      className="flex-1 rounded-none bg-[#A7795E] hover:bg-[#8A6240] text-[#E5E1DC]"
                    >
                      <Link href={event.registrationUrl}>Register Now</Link>
                    </NPIButton>
                  ) : (
                    <NPIButton
                      variant="outline"
                      className="flex-1 rounded-none border-[#CEC9BC] text-[#46372A]"
                      disabled
                    >
                      Registration Closed
                    </NPIButton>
                  )}
                  <NPIButton
                    asChild
                    variant="outline"
                    size="sm"
                    className="rounded-none border-[#8D8F78] text-[#2F2C29] hover:bg-[#E5E1DC]"
                  >
                    <Link href={`/events/${event.id}`}>
                      <ExternalLink className="w-4 h-4" />
                    </Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            ))}
          </div>
        </div>
      )}

      {/* Regular Events */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-6">
          <h3 className="text-2xl font-bold font-npi text-[#2F2C29]">All Events</h3>
          <div className="flex-1 h-0.5 bg-gradient-to-r from-[#8A6240] to-transparent"></div>
        </div>
      </div>
      <div className="grid lg:grid-cols-4 gap-6">
        {regularEvents.map((event) => (
          <NPICard
            key={event.id}
            className="hover:shadow-lg transition-shadow duration-300 rounded-none"
          >
            <NPICardHeader>
              <div className="flex items-center justify-between mb-2">
                <span className={`px-2 py-1 text-xs font-medium ${getTypeColor(event.type)}`}>
                  {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                </span>
                <span className="text-xs text-muted-foreground font-npi">{event.category}</span>
              </div>
              <NPICardTitle className="text-lg leading-tight">{event.title}</NPICardTitle>
            </NPICardHeader>

            <NPICardContent>
              <p className="text-muted-foreground text-sm leading-relaxed mb-4 font-npi">
                {event.description.substring(0, 100)}...
              </p>

              <div className="space-y-1 text-xs mb-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-3 h-3 text-muted-foreground" />
                  <span className="font-npi">{formatDate(event.date, event.endDate)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-3 h-3 text-muted-foreground" />
                  <span className="font-npi">{event.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-3 h-3 text-muted-foreground" />
                  <span className="font-npi">
                    {event.registered}/{event.capacity}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="font-semibold text-primary font-npi">{event.price}</span>
                <div className="flex gap-1">
                  {event.tags.slice(0, 2).map((tag, index) => (
                    <span
                      key={index}
                      className="px-1 py-0.5 bg-primary/10 text-primary text-xs rounded font-npi"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </NPICardContent>

            <NPICardFooter className="flex gap-2">
              {event.registrationUrl ? (
                <NPIButton
                  asChild
                  variant="outline"
                  className="flex-1 rounded-none border-[#A7795E] text-[#6E3C19] hover:bg-[#A7795E] hover:text-[#E5E1DC]"
                  size="sm"
                >
                  <Link href={event.registrationUrl}>Register</Link>
                </NPIButton>
              ) : (
                <NPIButton
                  variant="ghost"
                  className="flex-1 rounded-none text-[#8D8F78]"
                  size="sm"
                  disabled
                >
                  Closed
                </NPIButton>
              )}
              <NPIButton
                asChild
                variant="ghost"
                size="sm"
                className="rounded-none text-[#46372A] hover:bg-[#E5E1DC]"
              >
                <Link href={`/events/${event.id}`}>Details</Link>
              </NPIButton>
            </NPICardFooter>
          </NPICard>
        ))}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-[#46372A] font-npi">
          Showing <span className="font-bold text-[#A7795E]">{filteredEvents.length}</span> of{' '}
          <span className="font-bold text-[#A7795E]">{events.length}</span> events
        </p>
      </div>
    </NPISection>
  )
}
