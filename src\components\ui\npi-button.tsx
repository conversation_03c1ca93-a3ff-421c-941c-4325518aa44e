import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/utilities/ui'

const npiButtonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap text-sm font-medium font-npi transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md active:shadow-sm',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline:
          'border-2 border-primary bg-transparent text-primary hover:bg-primary hover:text-primary-foreground hover:shadow-lg',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/90',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline shadow-none hover:shadow-none',
        // NPI specific variants - clean and minimalistic
        primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
        brown: 'bg-primary text-primary-foreground hover:bg-primary/90',
        green: 'bg-secondary text-secondary-foreground hover:bg-secondary/90',
        cream:
          'bg-tertiary text-primary border-2 border-primary hover:bg-primary hover:text-primary-foreground',
        accent: 'bg-accent text-accent-foreground hover:bg-accent/90',
        glass: 'bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 border border-white/30',
        gradient:
          'bg-gradient-to-r from-primary to-primary/80 text-white hover:from-primary/90 hover:to-primary/70',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 px-3 text-xs',
        lg: 'h-11 px-8 text-base',
        xl: 'h-12 px-10 text-lg font-semibold',
        icon: 'h-10 w-10',
      },
      effect: {
        none: '',
        shimmer: 'npi-shimmer',
        float: 'npi-float',
        pulse: 'npi-pulse-on-hover',
        bounce: 'npi-bounce-on-hover',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      effect: 'none',
    },
  },
)

export interface NPIButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof npiButtonVariants> {
  asChild?: boolean
}

const NPIButton = React.forwardRef<HTMLButtonElement, NPIButtonProps>(
  ({ className, variant, size, effect, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    return (
      <Comp
        className={cn(npiButtonVariants({ variant, size, effect, className }))}
        ref={ref}
        {...props}
      />
    )
  },
)
NPIButton.displayName = 'NPIButton'

export { NPIButton, npiButtonVariants }
