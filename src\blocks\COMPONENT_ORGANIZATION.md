# Component Organization Structure

This document outlines the new page and sub-page based component organization for the NPI website.

## 📁 Directory Structure

```
src/blocks/
├── pages/                          # Page-specific components
│   ├── home/                       # Homepage components
│   ├── about/                      # About page and sub-pages
│   │   ├── main/                   # Main about page
│   │   ├── operations-structure/   # Operations structure sub-page
│   │   └── strategic-alignment/    # Strategic alignment sub-page
│   ├── partnerships/               # Partnerships page and sub-pages
│   │   ├── main/                   # Main partnerships page
│   │   ├── investment-opportunities/ # Investment opportunities sub-page
│   │   └── partners/               # Partners sub-page
│   ├── resources/                  # Resources page and sub-pages
│   │   ├── main/                   # Main resources page
│   │   └── media-gallery/          # Media gallery sub-page
│   ├── projects/                   # Projects page
│   ├── strategic-pillars/          # Strategic pillars page
│   ├── success-stories/            # Success stories page
│   ├── contact/                    # Contact page
│   ├── events/                     # Events page
│   ├── news/                       # News page
│   ├── get-involved/               # Get involved page
│   ├── posts/                      # Posts page
│   ├── search/                     # Search page
│   └── index.ts                    # Master export file
├── shared/                         # Shared/reusable components
└── [original component folders]    # Original component structure (unchanged)
```

## 🎯 Benefits

1. **Clear Page Association**: Components are grouped by their page usage
2. **Sub-page Organization**: Sub-pages have their own component groups
3. **Improved Maintainability**: Related components are co-located
4. **Better Developer Experience**: Intuitive navigation and imports
5. **Scalability**: Easy to add new pages and components

## 📋 Usage Examples

### Import from specific page
```typescript
import { NPIAboutHeroBlock } from '@/blocks/pages/about/main'
```

### Import multiple components from same page
```typescript
import {
  NPIInvestmentOpportunitiesHeroBlock,
  NPIInvestmentOpportunitiesBlock
} from '@/blocks/pages/partnerships/investment-opportunities'
```

### Import shared components
```typescript
import { NPIStatisticsBlock } from '@/blocks/shared'
```

### Import using namespaces
```typescript
import { About } from '@/blocks/pages'
// Use: About.AboutMain.NPIAboutHeroBlock
```

## 🔄 Migration Status

### ✅ Completed
- [x] Created page-based directory structure
- [x] Created index files for all pages and sub-pages
- [x] Updated homepage imports
- [x] Updated about page structure
- [x] Updated partnerships page structure
- [x] Updated strategic pillars page
- [x] Updated events page
- [x] Updated resources media gallery

### 🔄 In Progress
- [ ] Update remaining page imports
- [ ] Update RenderBlocks.tsx to use new structure
- [ ] Create convenience utilities

### 📝 Next Steps
1. Update all remaining page files to use new import structure
2. Update RenderBlocks.tsx to import from organized structure
3. Add TypeScript types for better development experience
4. Create utility functions for common import patterns

## 📖 Component Mapping

### Homepage Components
- `NPIIntroductionBlock` → `@/blocks/pages/home`
- `NPIMissionVisionBlock` → `@/blocks/pages/home`
- `NPIFeaturedProjectsBlock` → `@/blocks/pages/home`
- `NPISuccessStoriesBlock` → `@/blocks/pages/home`
- `NPIPartnersBlock` → `@/blocks/pages/home`
- `NPILatestUpdatesBlock` → `@/blocks/pages/home`

### About Page Components
- Main: `NPIAboutHeroBlock`, `NPIHistoryTimelineBlock` → `@/blocks/pages/about/main`
- Operations: `NPIOperationsHeroBlock`, `NPIOperationsStructureBlock` → `@/blocks/pages/about/operations-structure`
- Alignment: `NPIStrategicAlignmentHeroBlock`, `NPIStrategicAlignmentBlock` → `@/blocks/pages/about/strategic-alignment`

### Partnerships Components
- Main: `NPIPartnershipsHeroBlock`, `NPIPartnershipModelsBlock` → `@/blocks/pages/partnerships/main`
- Investment: `NPIInvestmentOpportunitiesHeroBlock`, `NPIInvestmentOpportunitiesBlock` → `@/blocks/pages/partnerships/investment-opportunities`
- Partners: `NPIPartnersHeroBlock`, `NPIPartnersShowcaseBlock` → `@/blocks/pages/partnerships/partners`

### Shared Components
- `NPIStatisticsBlock` → `@/blocks/shared` (used across multiple pages)
- `ArchiveBlock`, `BannerBlock`, `CallToActionBlock`, etc. → `@/blocks/shared`

## 🚀 Implementation Guidelines

1. **Always use the organized imports** for new components
2. **Gradually migrate existing imports** when working on files
3. **Keep original component folders** unchanged for backward compatibility
4. **Use shared components** for components used across multiple pages
5. **Follow the page hierarchy** when organizing new components
