import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPIPartners } from '@/components/ui/npi-partners'

interface NPIPartnersBlockProps {
  title?: string
  description?: string
  showNames?: boolean
}

export const NPIPartnersBlock: React.FC<NPIPartnersBlockProps> = ({
  title = 'Our Partners',
  description = "Working together with leading organizations to drive sustainable development and innovation in Kenya's natural products sector.",
  showNames = false,
}) => {
  const partners = [
    {
      name: 'Kenya Vision 2030',
      logo: 'https://picsum.photos/200/100?random=1',
      url: 'https://vision2030.go.ke',
      description: "Kenya's development blueprint",
    },
    {
      name: 'National Museums of Kenya',
      logo: 'https://picsum.photos/200/100?random=2',
      url: 'https://museums.or.ke',
      description: 'Cultural heritage preservation',
    },
    {
      name: '<PERSON><PERSON> (Bioinnovate Africa)',
      logo: 'https://picsum.photos/200/100?random=3',
      url: 'https://bioinnovate-africa.org',
      description: 'Bioscience innovation platform',
    },
    {
      name: 'Kenya Association of Manufacturers',
      logo: 'https://picsum.photos/200/100?random=4',
      url: 'https://kam.co.ke',
      description: 'Manufacturing sector development',
    },
    {
      name: 'University of Nairobi',
      logo: 'https://picsum.photos/200/100?random=5',
      url: 'https://uonbi.ac.ke',
      description: 'Research and academic partnership',
    },
    {
      name: 'Kenya Bureau of Standards',
      logo: 'https://picsum.photos/200/100?random=6',
      url: 'https://kebs.org',
      description: 'Quality standards and certification',
    },
    {
      name: 'Export Promotion Council',
      logo: 'https://picsum.photos/200/100?random=7',
      url: 'https://epckenya.org',
      description: 'Export development support',
    },
    {
      name: 'Kenya Industrial Research Institute',
      logo: 'https://picsum.photos/200/100?random=8',
      url: 'https://kirdi.go.ke',
      description: 'Industrial research and development',
    },
    {
      name: 'African Development Bank',
      logo: 'https://picsum.photos/200/100?random=9',
      url: 'https://afdb.org',
      description: 'Development financing',
    },
    {
      name: 'World Bank',
      logo: 'https://picsum.photos/200/100?random=10',
      url: 'https://worldbank.org',
      description: 'Global development partner',
    },
    {
      name: 'UNIDO',
      logo: 'https://picsum.photos/200/100?random=11',
      url: 'https://unido.org',
      description: 'Industrial development organization',
    },
    {
      name: 'GIZ Kenya',
      logo: '/images/partners/giz.png',
      url: 'https://giz.de',
      description: 'German development cooperation',
    },
  ]

  return (
    <NPISection className="bg-tertiary">
      <NPISectionHeader>
        <NPISectionTitle className="text-primary">{title}</NPISectionTitle>
        <NPISectionDescription className="font-light">{description}</NPISectionDescription>
      </NPISectionHeader>

      <NPIPartners partners={partners} variant="grid" showNames={showNames} />
    </NPISection>
  )
}
