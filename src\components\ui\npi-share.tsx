'use client'

import React, { useState } from 'react'
import { Share2, Facebook, Twitter, Linkedin, Link, Check } from 'lucide-react'
import { NPIButton } from '@/components/ui/npi-button'
import { NPICard, NPICardContent } from '@/components/ui/npi-card'
import { cn } from '@/utilities/ui'

interface NPIShareProps {
  url?: string
  title?: string
  description?: string
  className?: string
  variant?: 'button' | 'dropdown' | 'inline'
}

export const NPIShare: React.FC<NPIShareProps> = ({
  url,
  title = 'Natural Products Industry Initiative',
  description = "Transforming Kenya's natural heritage into sustainable economic opportunities",
  className,
  variant = 'button',
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [copied, setCopied] = useState(false)

  const shareUrl = url || (typeof window !== 'undefined' ? window.location.href : '')
  const encodedUrl = encodeURIComponent(shareUrl)
  const encodedTitle = encodeURIComponent(title)
  const encodedDescription = encodeURIComponent(description)

  const shareLinks = [
    {
      name: 'Facebook',
      icon: <Facebook className="w-4 h-4" />,
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
      color: 'hover:text-blue-600',
    },
    {
      name: 'Twitter',
      icon: <Twitter className="w-4 h-4" />,
      url: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
      color: 'hover:text-blue-400',
    },
    {
      name: 'LinkedIn',
      icon: <Linkedin className="w-4 h-4" />,
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
      color: 'hover:text-blue-700',
    },
  ]

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy: ', err)
    }
  }

  const handleShare = (shareUrl: string) => {
    window.open(shareUrl, '_blank', 'width=600,height=400')
  }

  if (variant === 'inline') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <span className="text-sm text-muted-foreground font-npi">Share:</span>
        {shareLinks.map((link) => (
          <button
            key={link.name}
            onClick={() => handleShare(link.url)}
            className={cn('p-2 rounded-lg hover:bg-muted transition-colors', link.color)}
            aria-label={`Share on ${link.name}`}
          >
            {link.icon}
          </button>
        ))}
        <button
          onClick={copyToClipboard}
          className="p-2 rounded-lg hover:bg-muted transition-colors hover:text-primary"
          aria-label="Copy link"
        >
          {copied ? <Check className="w-4 h-4" /> : <Link className="w-4 h-4" />}
        </button>
      </div>
    )
  }

  if (variant === 'dropdown') {
    return (
      <div className={cn('relative', className)}>
        <NPIButton variant="outline" size="sm" onClick={() => setIsOpen(!isOpen)} className="gap-2">
          <Share2 className="w-4 h-4" />
          Share
        </NPIButton>

        {isOpen && (
          <>
            <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
            <NPICard className="absolute top-full right-0 mt-2 w-48 z-50 shadow-lg">
              <NPICardContent className="p-3">
                <div className="space-y-2">
                  {shareLinks.map((link) => (
                    <button
                      key={link.name}
                      onClick={() => {
                        handleShare(link.url)
                        setIsOpen(false)
                      }}
                      className="w-full flex items-center gap-3 p-2 rounded-lg hover:bg-muted transition-colors text-left"
                    >
                      {link.icon}
                      <span className="font-npi">{link.name}</span>
                    </button>
                  ))}
                  <button
                    onClick={() => {
                      copyToClipboard()
                      setIsOpen(false)
                    }}
                    className="w-full flex items-center gap-3 p-2 rounded-lg hover:bg-muted transition-colors text-left"
                  >
                    {copied ? <Check className="w-4 h-4" /> : <Link className="w-4 h-4" />}
                    <span className="font-npi">{copied ? 'Copied!' : 'Copy Link'}</span>
                  </button>
                </div>
              </NPICardContent>
            </NPICard>
          </>
        )}
      </div>
    )
  }

  return (
    <NPIButton
      variant="outline"
      size="sm"
      onClick={() => setIsOpen(!isOpen)}
      className={cn('gap-2', className)}
    >
      <Share2 className="w-4 h-4" />
      Share
    </NPIButton>
  )
}
