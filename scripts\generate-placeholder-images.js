import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Create placeholder image data URLs for different categories
const placeholderImages = {
  hero: {
    'kenya-landscape.jpg':
      'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?w=1920&h=1080&fit=crop&crop=center',
    'npi-hero-bg.jpg':
      'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1920&h=1080&fit=crop&crop=center',
    'research-lab.jpg':
      'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=1920&h=1080&fit=crop&crop=center',
  },
  projects: {
    'research-development.jpg':
      'https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?w=800&h=600&fit=crop&crop=center',
    'community-engagement.jpg':
      'https://images.unsplash.com/photo-**********-cd4628902d4a?w=800&h=600&fit=crop&crop=center',
    'capacity-building.jpg':
      'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?w=800&h=600&fit=crop&crop=center',
    'innovation-hub.jpg':
      'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&h=600&fit=crop&crop=center',
  },
  success: {
    'success-story-1.jpg':
      'https://images.unsplash.com/photo-**********-cd4628902d4a?w=600&h=400&fit=crop&crop=center',
    'success-story-2.jpg':
      'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?w=600&h=400&fit=crop&crop=center',
    'success-story-3.jpg':
      'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=600&h=400&fit=crop&crop=center',
  },
  products: {
    'natural-product-1.jpg':
      'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=400&fit=crop&crop=center',
    'natural-product-2.jpg':
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop&crop=center',
    'natural-product-3.jpg':
      'https://images.unsplash.com/photo-1574263867128-a3d5c1b1deaa?w=400&h=400&fit=crop&crop=center',
  },
  communities: {
    'community-1.jpg':
      'https://images.unsplash.com/photo-**********-cd4628902d4a?w=600&h=400&fit=crop&crop=center',
    'community-2.jpg':
      'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?w=600&h=400&fit=crop&crop=center',
    'traditional-knowledge.jpg':
      'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=600&h=400&fit=crop&crop=center',
  },
  partners: {
    'nmk-logo.png': 'https://via.placeholder.com/200x100/3E2723/FFFFFF?text=NMK',
    'vision2030-logo.png': 'https://via.placeholder.com/200x100/2D5016/FFFFFF?text=Vision+2030',
    'beta-logo.png': 'https://via.placeholder.com/200x100/3E2723/FFFFFF?text=BeTA',
    'kam-logo.png': 'https://via.placeholder.com/200x100/2D5016/FFFFFF?text=KAM',
    'kebs-logo.png': 'https://via.placeholder.com/200x100/3E2723/FFFFFF?text=KEBS',
    'kirdi-logo.png': 'https://via.placeholder.com/200x100/2D5016/FFFFFF?text=KIRDI',
  },
  team: {
    'team-member-1.jpg':
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
    'team-member-2.jpg':
      'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
    'team-member-3.jpg':
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
  },
  events: {
    'conference-2024.jpg':
      'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?w=800&h=600&fit=crop&crop=center',
    'workshop-1.jpg':
      'https://images.unsplash.com/photo-**********-cd4628902d4a?w=800&h=600&fit=crop&crop=center',
    'seminar-1.jpg':
      'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?w=800&h=600&fit=crop&crop=center',
  },
}

// Create image reference file
const imageReferences = {
  hero: {
    'kenya-landscape.jpg': {
      alt: 'Beautiful Kenyan landscape showcasing natural heritage',
      description: "Scenic view of Kenya's diverse natural landscape",
      category: 'hero',
    },
    'npi-hero-bg.jpg': {
      alt: 'NPI initiative background image',
      description: 'Background image representing NPI mission',
      category: 'hero',
    },
    'research-lab.jpg': {
      alt: 'Modern research laboratory',
      description: 'State-of-the-art research facility for natural products',
      category: 'hero',
    },
  },
  projects: {
    'research-development.jpg': {
      alt: 'Research and development activities',
      description: 'Scientists working on natural product research',
      category: 'projects',
    },
    'community-engagement.jpg': {
      alt: 'Community engagement session',
      description: 'Local community members participating in NPI projects',
      category: 'projects',
    },
    'capacity-building.jpg': {
      alt: 'Capacity building workshop',
      description: 'Training session for local entrepreneurs',
      category: 'projects',
    },
    'innovation-hub.jpg': {
      alt: 'Innovation hub facility',
      description: 'Modern innovation center for natural products',
      category: 'projects',
    },
  },
  success: {
    'success-story-1.jpg': {
      alt: 'Success story - Community empowerment',
      description: 'Local community benefiting from NPI initiatives',
      category: 'success',
    },
    'success-story-2.jpg': {
      alt: 'Success story - Product development',
      description: 'Successful natural product development project',
      category: 'success',
    },
    'success-story-3.jpg': {
      alt: 'Success story - Economic growth',
      description: 'Economic impact of NPI programs',
      category: 'success',
    },
  },
  products: {
    'natural-product-1.jpg': {
      alt: 'Natural product sample',
      description: 'Traditional medicinal plant product',
      category: 'products',
    },
    'natural-product-2.jpg': {
      alt: 'Herbal medicine preparation',
      description: 'Processed herbal medicine from indigenous knowledge',
      category: 'products',
    },
    'natural-product-3.jpg': {
      alt: 'Natural cosmetic product',
      description: 'Natural cosmetic derived from local plants',
      category: 'products',
    },
  },
  communities: {
    'community-1.jpg': {
      alt: 'Local community members',
      description: 'Community members sharing traditional knowledge',
      category: 'communities',
    },
    'community-2.jpg': {
      alt: 'Traditional knowledge keepers',
      description: 'Elders preserving indigenous knowledge',
      category: 'communities',
    },
    'traditional-knowledge.jpg': {
      alt: 'Traditional knowledge documentation',
      description: 'Recording and preserving traditional practices',
      category: 'communities',
    },
  },
  partners: {
    'nmk-logo.png': {
      alt: 'National Museums of Kenya logo',
      description: 'Official logo of National Museums of Kenya',
      category: 'partners',
    },
    'vision2030-logo.png': {
      alt: 'Kenya Vision 2030 logo',
      description: 'Official logo of Kenya Vision 2030',
      category: 'partners',
    },
    'beta-logo.png': {
      alt: 'Bioinnovate Africa logo',
      description: 'Official logo of Bioinnovate Africa',
      category: 'partners',
    },
    'kam-logo.png': {
      alt: 'Kenya Association of Manufacturers logo',
      description: 'Official logo of Kenya Association of Manufacturers',
      category: 'partners',
    },
    'kebs-logo.png': {
      alt: 'Kenya Bureau of Standards logo',
      description: 'Official logo of Kenya Bureau of Standards',
      category: 'partners',
    },
    'kirdi-logo.png': {
      alt: 'Kenya Industrial Research and Development Institute logo',
      description: 'Official logo of KIRDI',
      category: 'partners',
    },
  },
  team: {
    'team-member-1.jpg': {
      alt: 'Dr. John Kamau - Project Director',
      description: 'Project Director with expertise in natural products',
      category: 'team',
    },
    'team-member-2.jpg': {
      alt: 'Dr. Mary Wanjiku - Research Lead',
      description: 'Lead researcher in traditional medicine',
      category: 'team',
    },
    'team-member-3.jpg': {
      alt: 'Prof. David Mwangi - Advisory Board',
      description: 'Advisory board member and industry expert',
      category: 'team',
    },
  },
  events: {
    'conference-2024.jpg': {
      alt: 'NPI Annual Conference 2024',
      description: 'Annual conference on natural products industry',
      category: 'events',
    },
    'workshop-1.jpg': {
      alt: 'Community workshop',
      description: 'Workshop on traditional knowledge documentation',
      category: 'events',
    },
    'seminar-1.jpg': {
      alt: 'Research seminar',
      description: 'Seminar on latest research findings',
      category: 'events',
    },
  },
}

// Write the image references to a JSON file
const outputPath = path.join(__dirname, '..', 'public', 'images', 'image-references.json')
fs.writeFileSync(outputPath, JSON.stringify(imageReferences, null, 2))

console.log('Image references created at:', outputPath)
console.log('Placeholder image URLs generated for development use.')
console.log('Replace with actual NPI images before production deployment.')
