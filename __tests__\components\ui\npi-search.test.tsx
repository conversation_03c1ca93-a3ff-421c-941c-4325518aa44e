import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { NPISearch } from '@/components/ui/npi-search'

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: any) {
    return <a href={href} {...props}>{children}</a>
  }
})

describe('NPISearch', () => {
  it('renders search input with placeholder', () => {
    render(<NPISearch placeholder="Search test..." />)
    const input = screen.getByPlaceholderText('Search test...')
    expect(input).toBeInTheDocument()
  })

  it('shows search icon', () => {
    render(<NPISearch />)
    const searchIcon = screen.getByRole('textbox').parentElement?.querySelector('svg')
    expect(searchIcon).toBeInTheDocument()
  })

  it('opens dropdown when input is focused', () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox')
    
    fireEvent.focus(input)
    
    expect(screen.getByText('Popular searches:')).toBeInTheDocument()
  })

  it('shows popular search terms', () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox')
    
    fireEvent.focus(input)
    
    expect(screen.getByText('IKIA')).toBeInTheDocument()
    expect(screen.getByText('Programs')).toBeInTheDocument()
    expect(screen.getByText('Success Stories')).toBeInTheDocument()
    expect(screen.getByText('Partnerships')).toBeInTheDocument()
  })

  it('performs search when typing', async () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox')
    
    fireEvent.focus(input)
    fireEvent.change(input, { target: { value: 'about' } })
    
    await waitFor(() => {
      expect(screen.getByText('About NPI')).toBeInTheDocument()
    })
  })

  it('shows loading state during search', async () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox')
    
    fireEvent.focus(input)
    fireEvent.change(input, { target: { value: 'test' } })
    
    expect(screen.getByText('Searching...')).toBeInTheDocument()
  })

  it('shows no results message for empty search', async () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox')
    
    fireEvent.focus(input)
    fireEvent.change(input, { target: { value: 'nonexistent' } })
    
    await waitFor(() => {
      expect(screen.getByText(/No results found for/)).toBeInTheDocument()
    })
  })

  it('clears search when clear button is clicked', () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox') as HTMLInputElement
    
    fireEvent.change(input, { target: { value: 'test query' } })
    expect(input.value).toBe('test query')
    
    const clearButton = screen.getByRole('button')
    fireEvent.click(clearButton)
    
    expect(input.value).toBe('')
  })

  it('closes dropdown when clicking outside', () => {
    render(
      <div>
        <NPISearch />
        <div data-testid="outside">Outside element</div>
      </div>
    )
    
    const input = screen.getByRole('textbox')
    fireEvent.focus(input)
    
    expect(screen.getByText('Popular searches:')).toBeInTheDocument()
    
    fireEvent.mouseDown(screen.getByTestId('outside'))
    
    expect(screen.queryByText('Popular searches:')).not.toBeInTheDocument()
  })

  it('navigates to search results when clicking on result', () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox')
    
    fireEvent.focus(input)
    fireEvent.change(input, { target: { value: 'about' } })
    
    waitFor(() => {
      const aboutLink = screen.getByText('About NPI').closest('a')
      expect(aboutLink).toHaveAttribute('href', '/about')
    })
  })
})

describe('NPISearch Accessibility', () => {
  it('has proper ARIA attributes', () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox')
    
    expect(input).toHaveAttribute('type', 'text')
    expect(input).toHaveAttribute('placeholder')
  })

  it('supports keyboard navigation', () => {
    render(<NPISearch />)
    const input = screen.getByRole('textbox')
    
    fireEvent.focus(input)
    expect(input).toHaveFocus()
    
    fireEvent.keyDown(input, { key: 'Escape' })
    // Should close dropdown (implementation dependent)
  })
})
