import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'News & Media - Natural Products Industry Initiative',
  description:
    "Stay informed about the latest developments, achievements, and insights from Kenya's natural products sector. Read news articles, press releases, and media coverage.",
}

const newsPageLayout = [
  {
    blockType: 'npiNewsHero' as const,
    title: 'News & Media',
    backgroundImage: '/assets/background.jpg',
  },
  {
    blockType: 'npiNewsListing' as const,
    id: 'latest-news',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Media Reach & Impact',
    variant: 'secondary',
  },
]

export default function NewsPage() {
  return (
    <article className="pb-24">
      <RenderBlocks blocks={newsPageLayout} />
    </article>
  )
}
