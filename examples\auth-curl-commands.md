# Complete Authentication Curl Commands

## ✅ **Now Working: Public Registration Enabled**

I've updated your Users collection to allow public registration by changing `create: authenticated` to `create: () => true`.

## 🔐 **All Authentication Endpoints**

### 1. **Register/Create User** ✨ (Now Works!)
```bash
# Create a new user account (public registration)
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "name": "<PERSON>"
  }'
```

**Success Response:**
```json
{
  "message": "User created successfully",
  "doc": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. **Login**
```bash
# Login to get JWT token
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

**Success Response:**
```json
{
  "message": "Auth Passed",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "exp": **********
}
```

### 3. **Logout**
```bash
# Logout (invalidate current session)
curl -X POST http://localhost:3000/api/users/logout \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Success Response:**
```json
{
  "message": "You have been logged out successfully."
}
```

### 4. **Refresh Token**
```bash
# Refresh JWT token (get new token with extended expiry)
curl -X POST http://localhost:3000/api/users/refresh-token \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Success Response:**
```json
{
  "message": "Token refresh successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "exp": 1640998800,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}
```

### 5. **Forgot Password**
```bash
# Request password reset (sends reset email if email is configured)
curl -X POST http://localhost:3000/api/users/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

**Success Response:**
```json
{
  "message": "Password reset email sent successfully"
}
```

### 6. **Reset Password**
```bash
# Reset password using token from email
curl -X POST http://localhost:3000/api/users/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "token": "RESET_TOKEN_FROM_EMAIL",
    "password": "mynewpassword123"
  }'
```

**Success Response:**
```json
{
  "message": "Password reset successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 7. **Get Current User (Me)**
```bash
# Get current authenticated user info
curl -X GET http://localhost:3000/api/users/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Success Response:**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "collection": "users",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "exp": **********
}
```

## 🔄 **Complete Flow Example**

```bash
# Step 1: Register a new user
echo "🔐 Registering new user..."
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User"
  }'

echo -e "\n\n📝 Logging in..."
# Step 2: Login to get token
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

# Extract token (requires jq)
TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token')
echo "Token: $TOKEN"

echo -e "\n\n👤 Getting current user..."
# Step 3: Get current user
curl -X GET http://localhost:3000/api/users/me \
  -H "Authorization: Bearer $TOKEN"

echo -e "\n\n🔄 Refreshing token..."
# Step 4: Refresh token
REFRESH_RESPONSE=$(curl -s -X POST http://localhost:3000/api/users/refresh-token \
  -H "Authorization: Bearer $TOKEN")

NEW_TOKEN=$(echo $REFRESH_RESPONSE | jq -r '.token')
echo "New Token: $NEW_TOKEN"

echo -e "\n\n🚪 Logging out..."
# Step 5: Logout
curl -X POST http://localhost:3000/api/users/logout \
  -H "Authorization: Bearer $NEW_TOKEN"

echo -e "\n\n✅ Complete authentication flow tested!"
```

## 🔧 **Testing Individual Endpoints**

### Test Registration Only:
```bash
curl -v -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User"
  }'
```

### Test Login Only:
```bash
curl -v -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Test Forgot Password:
```bash
curl -v -X POST http://localhost:3000/api/users/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

## ⚠️ **Important Notes**

1. **Registration is now public** - Anyone can create an account
2. **Email validation** - Payload validates email format automatically
3. **Password requirements** - Default minimum length is 6 characters
4. **Unique emails** - Payload prevents duplicate email addresses
5. **Email configuration** - Forgot password requires email setup in your environment

## 🛡️ **Security Considerations**

Since registration is now public, consider:

1. **Rate limiting** - Implement rate limiting for registration endpoint
2. **Email verification** - Consider requiring email verification
3. **CAPTCHA** - Add CAPTCHA for production registration
4. **User roles** - Consider adding user roles/permissions

## 📧 **Email Configuration (Optional)**

For forgot password to work, add email configuration to your `.env`:

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

Now registration should work! Try the curl command and let me know if you get any errors.
