'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>, Settings } from 'lucide-react'
import { NPIButton } from '@/components/ui/npi-button'
import { NPICard, NPICardContent } from '@/components/ui/npi-card'
import Link from 'next/link'

interface CookiePreferences {
  necessary: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

export const NPICookieBanner: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true,
    analytics: false,
    marketing: false,
    functional: false,
  })

  useEffect(() => {
    const consent = localStorage.getItem('npi-cookie-consent')
    if (!consent) {
      setIsVisible(true)
    }
  }, [])

  const acceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
    }
    setPreferences(allAccepted)
    saveCookiePreferences(allAccepted)
    setIsVisible(false)
  }

  const acceptNecessary = () => {
    const necessaryOnly = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
    }
    setPreferences(necessaryOnly)
    saveCookiePreferences(necessaryOnly)
    setIsVisible(false)
  }

  const saveCustomPreferences = () => {
    saveCookiePreferences(preferences)
    setIsVisible(false)
    setShowSettings(false)
  }

  const saveCookiePreferences = (prefs: CookiePreferences) => {
    localStorage.setItem(
      'npi-cookie-consent',
      JSON.stringify({
        preferences: prefs,
        timestamp: Date.now(),
      }),
    )

    // Initialize analytics based on preferences
    if (prefs.analytics) {
      // Initialize Google Analytics or other analytics
      console.log('Analytics enabled')
    }

    if (prefs.marketing) {
      // Initialize marketing cookies
      console.log('Marketing cookies enabled')
    }

    if (prefs.functional) {
      // Initialize functional cookies
      console.log('Functional cookies enabled')
    }
  }

  const handlePreferenceChange = (type: keyof CookiePreferences, value: boolean) => {
    setPreferences((prev) => ({
      ...prev,
      [type]: value,
    }))
  }

  if (!isVisible) {
    return null
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4">
      <NPICard className="max-w-4xl mx-auto shadow-2xl border-2">
        <NPICardContent className="p-6">
          {!showSettings ? (
            // Main banner
            <div className="flex items-start gap-4">
              <Cookie className="w-8 h-8 text-primary flex-shrink-0 mt-1" />
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2 font-npi">We Value Your Privacy</h3>
                <p className="text-muted-foreground mb-4 font-npi">
                  We use cookies to enhance your browsing experience, provide personalized content,
                  and analyze our traffic. By clicking &ldquo;Accept All&rdquo;, you consent to our
                  use of cookies. You can manage your preferences or learn more in our{' '}
                  <Link href="/privacy-policy" className="text-primary hover:underline">
                    Privacy Policy
                  </Link>
                  .
                </p>
                <div className="flex flex-wrap gap-3">
                  <NPIButton onClick={acceptAll} variant="primary">
                    Accept All
                  </NPIButton>
                  <NPIButton onClick={acceptNecessary} variant="outline">
                    Necessary Only
                  </NPIButton>
                  <NPIButton
                    onClick={() => setShowSettings(true)}
                    variant="ghost"
                    className="gap-2"
                  >
                    <Settings className="w-4 h-4" />
                    Customize
                  </NPIButton>
                </div>
              </div>
              <button
                onClick={() => setIsVisible(false)}
                className="text-muted-foreground hover:text-foreground transition-colors"
                aria-label="Close banner"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          ) : (
            // Settings panel
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="font-semibold text-lg font-npi">Cookie Preferences</h3>
                <button
                  onClick={() => setShowSettings(false)}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between p-3 border border-border rounded-lg">
                  <div>
                    <h4 className="font-medium font-npi">Necessary Cookies</h4>
                    <p className="text-sm text-muted-foreground font-npi">
                      Essential for the website to function properly. Cannot be disabled.
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.necessary}
                    disabled
                    className="w-4 h-4"
                  />
                </div>

                <div className="flex items-center justify-between p-3 border border-border rounded-lg">
                  <div>
                    <h4 className="font-medium font-npi">Analytics Cookies</h4>
                    <p className="text-sm text-muted-foreground font-npi">
                      Help us understand how visitors interact with our website.
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.analytics}
                    onChange={(e) => handlePreferenceChange('analytics', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>

                <div className="flex items-center justify-between p-3 border border-border rounded-lg">
                  <div>
                    <h4 className="font-medium font-npi">Marketing Cookies</h4>
                    <p className="text-sm text-muted-foreground font-npi">
                      Used to deliver personalized advertisements and track campaign performance.
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.marketing}
                    onChange={(e) => handlePreferenceChange('marketing', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>

                <div className="flex items-center justify-between p-3 border border-border rounded-lg">
                  <div>
                    <h4 className="font-medium font-npi">Functional Cookies</h4>
                    <p className="text-sm text-muted-foreground font-npi">
                      Enable enhanced functionality and personalization features.
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.functional}
                    onChange={(e) => handlePreferenceChange('functional', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <NPIButton onClick={saveCustomPreferences} variant="primary">
                  Save Preferences
                </NPIButton>
                <NPIButton onClick={acceptAll} variant="outline">
                  Accept All
                </NPIButton>
              </div>
            </div>
          )}
        </NPICardContent>
      </NPICard>
    </div>
  )
}
