'use client'

import { useEffect, useRef, useState } from 'react'

interface UseParallaxOptions {
  speed?: number
  offset?: number
  disabled?: boolean
}

export function useParallax({
  speed = 0.5,
  offset = 0,
  disabled = false,
}: UseParallaxOptions = {}) {
  const [isMounted, setIsMounted] = useState(false)
  const [scrollY, setScrollY] = useState(0)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    setIsMounted(true)

    const handleScroll = () => {
      if (ref.current && !disabled) {
        const rect = ref.current.getBoundingClientRect()
        const scrolled = window.scrollY
        const elementTop = rect.top + scrolled
        const elementHeight = rect.height
        const windowHeight = window.innerHeight

        // Calculate progress from when element enters viewport to when it leaves
        const start = elementTop - windowHeight
        const end = elementTop + elementHeight
        const progress = Math.max(0, Math.min(1, (scrolled - start) / (end - start)))

        const parallaxValue = offset + progress * speed * 100
        setScrollY(parallaxValue)
      }
    }

    if (isMounted) {
      handleScroll()
      window.addEventListener('scroll', handleScroll, { passive: true })
      window.addEventListener('resize', handleScroll, { passive: true })
    }

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleScroll)
    }
  }, [isMounted, speed, offset, disabled])

  return {
    ref,
    y: isMounted && !disabled ? scrollY : 0,
    isMounted,
  }
}

export function useScrollAnimation() {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '-50px',
      },
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [])

  return { ref, isVisible }
}

export function useStaggeredAnimation(itemCount: number, delay: number = 0.1) {
  const [visibleItems, setVisibleItems] = useState<number[]>([])
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          // Stagger the animation of items
          for (let i = 0; i < itemCount; i++) {
            setTimeout(
              () => {
                setVisibleItems((prev) => [...prev, i])
              },
              i * delay * 1000,
            )
          }
        }
      },
      {
        threshold: 0.1,
        rootMargin: '-50px',
      },
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [itemCount, delay])

  return { ref, visibleItems }
}

export function useMouseParallax(strength: number = 0.1) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!ref.current) return

      const rect = ref.current.getBoundingClientRect()
      const centerX = rect.left + rect.width / 2
      const centerY = rect.top + rect.height / 2

      const deltaX = (e.clientX - centerX) * strength
      const deltaY = (e.clientY - centerY) * strength

      setMousePosition({ x: deltaX, y: deltaY })
    }

    const handleMouseLeave = () => {
      setMousePosition({ x: 0, y: 0 })
    }

    if (ref.current) {
      ref.current.addEventListener('mousemove', handleMouseMove)
      ref.current.addEventListener('mouseleave', handleMouseLeave)
    }

    return () => {
      if (ref.current) {
        ref.current.removeEventListener('mousemove', handleMouseMove)
        ref.current.removeEventListener('mouseleave', handleMouseLeave)
      }
    }
  }, [strength])

  return { ref, mousePosition }
}

export function useScrollProgress() {
  const [scrollProgress, setScrollProgress] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = window.scrollY / totalHeight
      setScrollProgress(Math.min(Math.max(progress, 0), 1))
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return scrollProgress
}

export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return isMounted ? prefersReducedMotion : false
}
