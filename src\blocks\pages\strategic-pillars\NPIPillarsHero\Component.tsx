import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Database, Lightbulb, Users, Shield, ArrowDown } from 'lucide-react'

interface NPIPillarsHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPIPillarsHeroBlock: React.FC<NPIPillarsHeroProps> = ({
  title = 'Strategic Pillars',
  subtitle = "Four interconnected pillars that form the foundation of NPI's comprehensive approach to transforming Kenya's natural heritage into sustainable economic opportunities.",
  backgroundImage = '/assets/hero image.jpg',
}) => {
  const pillars = [
    {
      icon: <Database className="w-6 h-6" />,
      title: 'Knowledge Documentation',
      description: 'Preserving traditional wisdom',
    },
    {
      icon: <Lightbulb className="w-6 h-6" />,
      title: 'Product Development',
      description: 'Innovation and commercialization',
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: 'Capacity Building',
      description: 'Empowering communities',
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'IP Protection',
      description: 'Safeguarding rights',
    },
  ]

  return (
    <section className="h-[100vh] relative overflow-hidden -mt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Strategic Pillars background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#2F2C29]/80 via-[#34170D]/70 to-[#102820]/80 z-10"></div>

      {/* Content */}
      <div className="relative z-20 h-full flex flex-col">
        {/* Top Left Section - Title */}
        <div className="flex-1 flex items-start justify-start pt-32 px-4 sm:px-6 lg:px-25">
          <div className="max-w-2xl">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 font-npi leading-tight">
              {title}
            </h1>
            <p className="text-lg sm:text-xl text-[#E5E1DC]/90 font-npi leading-relaxed max-w-xl">
              {subtitle}
            </p>
          </div>
        </div>

        {/* Bottom Right Feature Card */}
        <div className="absolute bottom-8 right-8 z-30">
          <div className="bg-[#CEC9BC]/95 backdrop-blur-md p-6 shadow-lg max-w-md border border-[#8D8F78]/30">
            <h3 className="text-lg sm:text-xl font-bold mb-4 text-[#34170D]">
              Four Strategic Pillars
            </h3>

            {/* Pillars Grid */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              {pillars.map((pillar, index) => (
                <div
                  key={index}
                  className="bg-[#E5E1DC]/80 p-3 border border-[#8D8F78]/20 hover:bg-[#CABA9C]/80 transition-all duration-300"
                >
                  <div className="bg-[#6E3C19] w-8 h-8 flex items-center justify-center mb-2 text-white">
                    {pillar.icon}
                  </div>
                  <h4 className="font-semibold text-[#34170D] text-sm mb-1 font-npi">
                    {pillar.title}
                  </h4>
                  <p className="text-[#46372A] text-xs font-npi leading-tight">
                    {pillar.description}
                  </p>
                </div>
              ))}
            </div>

            <Link
              href="#pillars-detail"
              className="inline-flex items-center text-[#6E3C19] hover:text-[#A7795E] text-sm font-medium transition-colors"
            >
              <ArrowDown className="w-4 h-4 mr-2" />
              Explore Pillars &rarr;
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
