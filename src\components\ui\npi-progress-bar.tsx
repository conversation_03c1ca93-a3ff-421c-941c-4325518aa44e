'use client'

import React, { useState, useEffect } from 'react'
import { cn } from '@/utilities/ui'

interface NPIProgressBarProps {
  className?: string
  color?: string
  height?: number
}

export const NPIProgressBar: React.FC<NPIProgressBarProps> = ({
  className,
  color = 'bg-primary',
  height = 3,
}) => {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const updateProgress = () => {
      const scrollTop = window.pageYOffset
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollPercent = (scrollTop / docHeight) * 100
      setProgress(scrollPercent)
    }

    window.addEventListener('scroll', updateProgress)
    return () => window.removeEventListener('scroll', updateProgress)
  }, [])

  return (
    <div
      className={cn('fixed top-0 left-0 w-full z-50 bg-gray-200', className)}
      style={{ height: `${height}px` }}
    >
      <div
        className={cn('h-full transition-all duration-150 ease-out', color)}
        style={{ width: `${progress}%` }}
      />
    </div>
  )
}
