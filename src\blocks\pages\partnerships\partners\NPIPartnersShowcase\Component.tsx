'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPI<PERSON>ard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import { X, ExternalLink, MapPin, Calendar, Users } from 'lucide-react'

interface Partner {
  id: string
  name: string
  logo: string
  category: string
  location: string
  established: string
  description: string
  fullBio: string
  website?: string
  keyAchievements: string[]
  collaborationAreas: string[]
  partnershipSince: string
}

interface NPIPartnersShowcaseProps {
  title?: string
  description?: string
  partners?: Partner[]
}

export const NPIPartnersShowcaseBlock: React.FC<NPIPartnersShowcaseProps> = ({
  title = 'Partner Network',
  description = "Discover the diverse organizations and institutions that collaborate with us to drive sustainable development in Kenya's natural products sector.",
  partners = [
    {
      id: 'partner-1',
      name: 'Partner 1',
      logo: 'https://picsum.photos/200/200?random=1',
      category: 'Government Initiative',
      location: 'Nairobi, Kenya',
      established: '2008',
      description:
        'Strategic government partnership focused on policy development and economic alignment.',
      fullBio:
        'Partner 1 represents a key government initiative that aligns with our natural products development goals. This partnership focuses on policy framework development and strategic economic planning to support sustainable growth in the natural products sector.',
      website: 'https://example.com',
      keyAchievements: [
        'Policy framework development for natural products',
        'Integration of traditional knowledge systems',
        'Economic pillar alignment with NPI objectives',
      ],
      collaborationAreas: ['Policy Development', 'Economic Planning', 'Strategic Alignment'],
      partnershipSince: '2019',
    },
    {
      id: 'partner-2',
      name: 'Partner 2',
      logo: 'https://picsum.photos/200/200?random=2',
      category: 'Cultural Institution',
      location: 'Mombasa, Kenya',
      established: '1910',
      description:
        'Cultural institution dedicated to preserving traditional knowledge and heritage.',
      fullBio:
        'Partner 2 is a leading cultural institution that collaborates with us on documenting traditional knowledge systems, preserving cultural practices related to natural products, and ensuring that indigenous wisdom is respected and protected.',
      website: 'https://example.com',
      keyAchievements: [
        'Traditional knowledge documentation',
        'Cultural heritage preservation',
        'Community engagement programs',
      ],
      collaborationAreas: [
        'Cultural Preservation',
        'Traditional Knowledge',
        'Community Engagement',
      ],
      partnershipSince: '2020',
    },
    {
      id: 'partner-3',
      name: 'Partner 3',
      logo: 'https://picsum.photos/200/200?random=3',
      category: 'Research Institution',
      location: 'Kisumu, Kenya',
      established: '1995',
      description:
        'Leading research institution advancing natural products science and innovation.',
      fullBio:
        'Partner 3 is a premier research institution that conducts cutting-edge research in natural products development, sustainable extraction methods, and community-based innovation programs.',
      website: 'https://example.com',
      keyAchievements: [
        'Breakthrough research in natural compounds',
        'Sustainable extraction technology development',
        'Community-based research programs',
      ],
      collaborationAreas: ['Research & Development', 'Innovation', 'Technology Transfer'],
      partnershipSince: '2018',
    },
    {
      id: 'partner-4',
      name: 'Partner 4',
      logo: 'https://picsum.photos/200/200?random=4',
      category: 'International NGO',
      location: 'Eldoret, Kenya',
      established: '2005',
      description:
        'International organization supporting sustainable development and community empowerment.',
      fullBio:
        'Partner 4 is an international NGO that works closely with local communities to promote sustainable development practices and empower communities through natural products initiatives.',
      website: 'https://example.com',
      keyAchievements: [
        'Community empowerment programs',
        'Sustainable development initiatives',
        'International funding facilitation',
      ],
      collaborationAreas: ['Community Development', 'Sustainability', 'International Relations'],
      partnershipSince: '2021',
    },
    {
      id: 'partner-5',
      name: 'Partner 5',
      logo: 'https://picsum.photos/200/200?random=5',
      category: 'Private Sector',
      location: 'Nakuru, Kenya',
      established: '2010',
      description: 'Private sector leader in natural products manufacturing and distribution.',
      fullBio:
        'Partner 5 is a leading private sector company that specializes in natural products manufacturing, quality control, and market distribution, providing valuable industry expertise and market access.',
      website: 'https://example.com',
      keyAchievements: [
        'Market expansion initiatives',
        'Quality assurance systems',
        'Supply chain optimization',
      ],
      collaborationAreas: ['Manufacturing', 'Quality Control', 'Market Access'],
      partnershipSince: '2022',
    },
    {
      id: 'partner-6',
      name: 'Partner 6',
      logo: 'https://picsum.photos/200/200?random=6',
      category: 'Academic Institution',
      location: 'Thika, Kenya',
      established: '1970',
      description: 'Academic institution providing education and research in natural sciences.',
      fullBio:
        'Partner 6 is a renowned academic institution that offers specialized programs in natural sciences and conducts collaborative research projects that advance our understanding of natural products.',
      website: 'https://example.com',
      keyAchievements: [
        'Educational program development',
        'Student research initiatives',
        'Academic publication contributions',
      ],
      collaborationAreas: ['Education', 'Research', 'Capacity Building'],
      partnershipSince: '2017',
    },
    {
      id: 'partner-7',
      name: 'Partner 7',
      logo: 'https://picsum.photos/200/200?random=7',
      category: 'Community Organization',
      location: 'Machakos, Kenya',
      established: '2012',
      description:
        'Community-based organization promoting local development and traditional practices.',
      fullBio:
        'Partner 7 is a grassroots community organization that works directly with local communities to preserve traditional practices and promote sustainable development through natural products initiatives.',
      website: 'https://example.com',
      keyAchievements: [
        'Community mobilization programs',
        'Traditional practice preservation',
        'Local capacity building',
      ],
      collaborationAreas: ['Community Engagement', 'Traditional Practices', 'Local Development'],
      partnershipSince: '2023',
    },
    {
      id: 'partner-8',
      name: 'Partner 8',
      logo: 'https://picsum.photos/200/200?random=8',
      category: 'Technology Partner',
      location: 'Nyeri, Kenya',
      established: '2015',
      description: 'Technology company providing digital solutions for natural products sector.',
      fullBio:
        'Partner 8 is an innovative technology company that develops digital solutions, data management systems, and technological tools that enhance efficiency and transparency in the natural products sector.',
      website: 'https://example.com',
      keyAchievements: [
        'Digital platform development',
        'Data management solutions',
        'Technology integration programs',
      ],
      collaborationAreas: ['Technology Development', 'Digital Solutions', 'Data Management'],
      partnershipSince: '2021',
    },
  ],
}) => {
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null)

  const getColorByIndex = (index: number) => {
    const colors = [
      'from-[#E5E1DC] to-[#CEC9BC]', // Light cream gradient
      'from-[#6E3C19] to-[#A7795E]', // Rich brown to warm tan
      'from-[#102820] to-[#4C6444]', // Deep green to forest green
      'from-[#34170D] to-[#8A6240]', // Dark brown to medium brown
      'from-[#CABA9C] to-[#8D8F78]', // Warm beige to olive
      'from-[#4D2D18] to-[#6E3C19]', // Dark chocolate to rich brown
      'from-[#4C6444] to-[#102820]', // Forest green to deep green
      'from-[#8A6240] to-[#CABA9C]', // Medium brown to warm beige
    ]
    return colors[index % colors.length]
  }

  const getTextColorByIndex = (index: number) => {
    const lightBackgrounds = [0, 4, 7] // E5E1DC, CABA9C based gradients
    return lightBackgrounds.includes(index % 8) ? 'text-[#141311]' : 'text-[#E5E1DC]'
  }

  return (
    <NPISection className="bg-[#F8F6F0]">
      <NPISectionHeader>
        <NPISectionTitle className="text-[#34170D]">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#46372A]">{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Partners Grid - 4 cards per row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {partners.map((partner, index) => (
          <motion.div
            key={partner.id}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            whileHover={{ y: -8, scale: 1.02 }}
          >
            <NPICard
              className={`cursor-pointer h-full flex flex-col aspect-square bg-gradient-to-br ${getColorByIndex(index)} border-2 border-[#8D8F78]/30 hover:border-[#6E3C19]/50 hover:shadow-xl transition-all duration-300 overflow-hidden`}
              onClick={() => setSelectedPartner(partner)}
            >
              <NPICardHeader className="flex-shrink-0 p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 bg-white/20 border border-white/30 flex items-center justify-center mb-3 overflow-hidden">
                    <Image
                      src={partner.logo}
                      alt={`${partner.name} logo`}
                      width={64}
                      height={64}
                      className="object-cover"
                    />
                  </div>
                  <NPICardTitle
                    className={`text-lg mb-2 leading-tight ${getTextColorByIndex(index)}`}
                  >
                    {partner.name}
                  </NPICardTitle>
                  <span
                    className={`px-2 py-1 text-xs font-medium bg-white/20 ${getTextColorByIndex(index)}`}
                  >
                    {partner.category}
                  </span>
                </div>
              </NPICardHeader>

              <NPICardContent className="p-4 flex-1 flex flex-col">
                <div className="space-y-3 flex-1">
                  <div className="flex items-center gap-2">
                    <MapPin className={`w-4 h-4 ${getTextColorByIndex(index)}`} />
                    <span className={`text-sm ${getTextColorByIndex(index)}/80`}>
                      {partner.location}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className={`w-4 h-4 ${getTextColorByIndex(index)}`} />
                    <span className={`text-sm ${getTextColorByIndex(index)}/80`}>
                      Since {partner.partnershipSince}
                    </span>
                  </div>
                  <p
                    className={`text-sm leading-tight ${getTextColorByIndex(index)}/90 line-clamp-3`}
                  >
                    {partner.description}
                  </p>
                </div>

                <div className="mt-3 pt-3 border-t border-white/20">
                  <NPIButton
                    size="sm"
                    variant="outline"
                    className={`w-full text-xs ${getTextColorByIndex(index)} border-current hover:bg-white/20`}
                  >
                    View Details
                  </NPIButton>
                </div>
              </NPICardContent>
            </NPICard>
          </motion.div>
        ))}
      </div>

      {/* Modal */}
      <AnimatePresence>
        {selectedPartner && (
          <motion.div
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedPartner(null)}
          >
            <motion.div
              className="bg-[#E5E1DC] max-w-2xl w-full max-h-[90vh] overflow-y-auto border-2 border-[#8D8F78]"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-[#34170D] to-[#6E3C19] p-6 text-white">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-white/20 border border-white/30 flex items-center justify-center overflow-hidden">
                      <Image
                        src={selectedPartner.logo}
                        alt={`${selectedPartner.name} logo`}
                        width={64}
                        height={64}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-1 font-npi">{selectedPartner.name}</h3>
                      <span className="px-3 py-1 bg-white/20 text-sm font-medium">
                        {selectedPartner.category}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedPartner(null)}
                    className="p-2 hover:bg-white/20 transition-colors"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 text-[#34170D] font-npi">
                      Partner Information
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-[#6E3C19]" />
                        <span>{selectedPartner.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-[#6E3C19]" />
                        <span>Established {selectedPartner.established}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-[#6E3C19]" />
                        <span>Partner since {selectedPartner.partnershipSince}</span>
                      </div>
                      {selectedPartner.website && (
                        <div className="flex items-center gap-2">
                          <ExternalLink className="w-4 h-4 text-[#6E3C19]" />
                          <a
                            href={selectedPartner.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-[#6E3C19] hover:underline"
                          >
                            Visit Website
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3 text-[#34170D] font-npi">
                      Collaboration Areas
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedPartner.collaborationAreas.map((area, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-[#8D8F78]/20 text-[#34170D] text-xs"
                        >
                          {area}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-[#34170D] font-npi">About</h4>
                  <p className="text-[#46372A] leading-relaxed">{selectedPartner.fullBio}</p>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-[#34170D] font-npi">Key Achievements</h4>
                  <ul className="space-y-2">
                    {selectedPartner.keyAchievements.map((achievement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-[#6E3C19] mt-2 flex-shrink-0"></div>
                        <span className="text-[#46372A] text-sm">{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </NPISection>
  )
}
