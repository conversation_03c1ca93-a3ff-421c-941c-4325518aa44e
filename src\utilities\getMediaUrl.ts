import { getClientSideURL } from '@/utilities/getURL'

/**
 * Processes media resource URL to ensure proper formatting
 * @param url The original URL from the resource
 * @param cacheTag Optional cache tag to append to the URL
 * @returns Properly formatted URL with cache tag if provided
 */
export const getMediaUrl = (url: string | null | undefined, cacheTag?: string | null): string => {
  if (!url) return ''

  // Check if URL already has http/https protocol
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return cacheTag ? `${url}?${cacheTag}` : url
  }

  // For production, use direct static file URLs instead of API routes
  // This ensures images work properly on Vercel
  if (url.startsWith('/api/media/file/')) {
    // Convert API route to direct static file path
    const staticPath = url.replace('/api/media/file/', '/media/')
    const baseUrl = getClientSideURL()
    return cacheTag ? `${baseUrl}${staticPath}?${cacheTag}` : `${baseUrl}${staticPath}`
  }

  // Otherwise prepend client-side URL
  const baseUrl = getClientSideURL()
  return cacheTag ? `${baseUrl}${url}?${cacheTag}` : `${baseUrl}${url}`
}
