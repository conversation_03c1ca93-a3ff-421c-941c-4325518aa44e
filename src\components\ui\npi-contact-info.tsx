'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Mail, Phone, MapPin, Clock, Globe, Users } from 'lucide-react'
import { NPI<PERSON>ard, NPICardHeader, NPICardTitle, NPICardContent } from './npi-card'

interface ContactMethod {
  icon: React.ReactNode
  title: string
  details: string[]
  link?: string
}

interface NPIContactInfoProps {
  title?: string
  description?: string
  contactMethods?: ContactMethod[]
  className?: string
}

export const NPIContactInfo: React.FC<NPIContactInfoProps> = ({
  title = 'Get in Touch',
  description = "Connect with the NPI team through multiple channels. We're here to support your natural products journey.",
  contactMethods = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: 'Visit Our Office',
      details: [
        'National Museums of Kenya',
        'Museum Hill Road',
        'P.O. Box 40658-00100',
        'Nairobi, Kenya',
      ],
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: 'Call or Email Us',
      details: ['+254 20 374 2131', '+254 20 374 2161', '<EMAIL>', '<EMAIL>'],
      link: 'tel:+254203742131',
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: 'Office Hours',
      details: [
        'Monday - Friday: 8:00 AM - 5:00 PM',
        'Saturday: 9:00 AM - 1:00 PM',
        'Sunday: Closed',
        'Public Holidays: Closed',
      ],
    },
  ],
  className = '',
}) => {
  return (
    <div className={`w-full ${className}`}>
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[#8A3E25] font-npi mb-3">{title}</h2>
        <p className="text-base text-[#725242] font-npi max-w-2xl mx-auto">{description}</p>
      </div>

      {/* Contact Methods Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {contactMethods.map((method, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
          >
            <NPICard className="h-full bg-white border-2 border-[#725242]/30 hover:border-[#25718A]/60 transition-all duration-300 hover:shadow-lg hover:scale-105">
              <NPICardHeader>
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-[#725242] flex items-center justify-center text-white shadow-md">
                    {method.icon}
                  </div>
                  <NPICardTitle className="text-base font-semibold text-[#8A3E25] font-npi">
                    {method.title}
                  </NPICardTitle>
                </div>
              </NPICardHeader>
              <NPICardContent>
                <div className="space-y-1">
                  {method.details.map((detail, detailIndex) => (
                    <div key={detailIndex}>
                      {method.link && detailIndex === 0 ? (
                        <a
                          href={method.link}
                          className="text-[#725242] font-npi hover:text-[#25718A] transition-colors duration-200 hover:underline text-sm font-medium"
                        >
                          {detail}
                        </a>
                      ) : detail.includes('@') ? (
                        <a
                          href={`mailto:${detail.includes(':') ? detail.split(': ')[1] : detail}`}
                          className="text-[#725242] font-npi hover:text-[#25718A] transition-colors duration-200 hover:underline text-sm font-medium"
                        >
                          {detail}
                        </a>
                      ) : detail.startsWith('+254') ? (
                        <a
                          href={`tel:${detail.replace(/\s/g, '')}`}
                          className="text-[#725242] font-npi hover:text-[#25718A] transition-colors duration-200 hover:underline text-sm font-medium"
                        >
                          {detail}
                        </a>
                      ) : (
                        <p className="text-[#725242] font-npi text-sm">{detail}</p>
                      )}
                    </div>
                  ))}
                </div>
              </NPICardContent>
            </NPICard>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
