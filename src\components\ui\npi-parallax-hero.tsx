'use client'

import * as React from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import { cn } from '@/utilities/ui'
import { useParallax, useReducedMotion } from '@/hooks/useParallax'

interface NPIParallaxHeroProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  backgroundImage?: string
  backgroundVideo?: string
  overlayOpacity?: number
  height?: 'screen' | 'large' | 'medium' | 'small'
  parallaxSpeed?: number
  variant?: 'default' | 'gradient' | 'minimal'
}

const NPIParallaxHero = React.forwardRef<HTMLDivElement, NPIParallaxHeroProps>(
  (
    {
      className,
      children,
      backgroundImage,
      backgroundVideo,
      overlayOpacity = 0.4,
      height = 'screen',
      parallaxSpeed = 0.5,
      variant = 'default',
      ...props
    },
    ref,
  ) => {
    const containerRef = React.useRef<HTMLDivElement>(null)
    const prefersReducedMotion = useReducedMotion()

    const { scrollYProgress } = useScroll({
      target: containerRef,
      offset: ['start start', 'end start'],
    })

    const y = useTransform(
      scrollYProgress,
      [0, 1],
      [0, prefersReducedMotion ? 0 : 300 * parallaxSpeed],
    )

    const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0])
    const scale = useTransform(scrollYProgress, [0, 1], [1, prefersReducedMotion ? 1 : 1.1])

    const heightClasses = {
      screen: 'min-h-screen',
      large: 'min-h-[80vh]',
      medium: 'min-h-[60vh]',
      small: 'min-h-[40vh]',
    }

    const variantClasses = {
      default: '',
      gradient: 'bg-gradient-to-br from-npi-burgundy-900 via-npi-burgundy-700 to-npi-green-900',
      minimal: 'bg-npi-cream',
    }

    return (
      <div
        ref={containerRef}
        className={cn(
          'relative overflow-hidden flex items-center justify-center',
          heightClasses[height],
          variantClasses[variant],
          className,
        )}
        {...props}
      >
        {/* Background Media */}
        {(backgroundImage || backgroundVideo) && (
          <motion.div className="absolute inset-0 z-0" style={{ y, scale }}>
            {backgroundVideo ? (
              <video autoPlay muted loop playsInline className="w-full h-full object-cover">
                <source src={backgroundVideo} type="video/mp4" />
              </video>
            ) : backgroundImage ? (
              <div
                className="w-full h-full bg-cover bg-center bg-no-repeat"
                style={{ backgroundImage: `url(${backgroundImage})` }}
              />
            ) : null}

            {/* Overlay */}
            <div
              className="absolute inset-0 bg-gradient-to-br from-[#34170D]/75 via-[#46372A]/55 via-[#4D2D18]/50 to-[#102820]/65"
              style={{ opacity: overlayOpacity }}
            />
          </motion.div>
        )}

        {/* Animated Background Elements */}
        <div className="absolute inset-0 z-10 pointer-events-none">
          <motion.div
            className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-[#A7795E]/20 blur-3xl"
            animate={{
              x: [0, 30, 0],
              y: [0, -20, 0],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
          <motion.div
            className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-[#4C6444]/18 blur-3xl"
            animate={{
              x: [0, -40, 0],
              y: [0, 25, 0],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 2,
            }}
          />
          <motion.div
            className="absolute top-1/2 right-1/3 w-48 h-48 rounded-full bg-[#8A6240]/15 blur-2xl"
            animate={{
              x: [0, -20, 0],
              y: [0, 15, 0],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 4,
            }}
          />
          <motion.div
            className="absolute top-3/4 left-1/3 w-32 h-32 rounded-full bg-[#CABA9C]/12 blur-xl"
            animate={{
              x: [0, 25, 0],
              y: [0, -15, 0],
            }}
            transition={{
              duration: 14,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 6,
            }}
          />
        </div>

        {/* Content */}
        <motion.div className="relative z-20 w-full" style={{ opacity }}>
          {children}
        </motion.div>
      </div>
    )
  },
)
NPIParallaxHero.displayName = 'NPIParallaxHero'

const NPIParallaxHeroContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white', className)}
    {...props}
  />
))
NPIParallaxHeroContent.displayName = 'NPIParallaxHeroContent'

const NPIParallaxHeroTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => {
  const { onDrag, onDragStart, onDragEnd, ...restProps } = props as any
  return (
    <motion.h1
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className={cn(
        'text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 leading-tight font-npi',
        className,
      )}
      {...restProps}
    >
      {children}
    </motion.h1>
  )
})
NPIParallaxHeroTitle.displayName = 'NPIParallaxHeroTitle'

const NPIParallaxHeroSubtitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { onDrag, onDragStart, onDragEnd, ...restProps } = props as any
  return (
    <motion.p
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.4 }}
      className={cn(
        'text-lg md:text-xl lg:text-2xl mb-8 text-white/90 max-w-3xl mx-auto leading-relaxed font-npi',
        className,
      )}
      {...restProps}
    >
      {children}
    </motion.p>
  )
})
NPIParallaxHeroSubtitle.displayName = 'NPIParallaxHeroSubtitle'

const NPIParallaxHeroActions = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  const { onDrag, onDragStart, onDragEnd, ...restProps } = props as any
  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.6 }}
      className={cn('flex flex-col sm:flex-row gap-4 justify-center items-center', className)}
      {...restProps}
    >
      {children}
    </motion.div>
  )
})
NPIParallaxHeroActions.displayName = 'NPIParallaxHeroActions'

export {
  NPIParallaxHero,
  NPIParallaxHeroContent,
  NPIParallaxHeroTitle,
  NPIParallaxHeroSubtitle,
  NPIParallaxHeroActions,
}
