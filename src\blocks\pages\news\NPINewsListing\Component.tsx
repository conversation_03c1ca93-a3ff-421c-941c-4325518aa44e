'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPI<PERSON>ard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import { Calendar, User, Tag, ArrowRight, Filter, Search, Eye } from 'lucide-react'
import { NPINewsletter } from '@/components/ui/npi-newsletter'

interface NewsArticle {
  id: string
  title: string
  excerpt: string
  content: string
  image: string
  author: string
  date: string
  category: string
  tags: string[]
  readTime: string
  views: number
  featured?: boolean
}

interface NPINewsListingProps {
  title?: string
  description?: string
  articles?: NewsArticle[]
}

export const NPINewsListingBlock: React.FC<NPINewsListingProps> = ({
  title = 'Latest News & Updates',
  description = "Stay informed about the latest developments, achievements, and insights from Kenya's natural products sector.",
  articles = [
    {
      id: 'npi-launches-documentation-platform',
      title: 'NPI Launches Revolutionary Knowledge Documentation Platform',
      excerpt:
        "A new digital platform now provides enhanced access to Kenya's traditional knowledge heritage, supporting researchers and communities nationwide.",
      content: 'Full article content here...',
      image: '/assets/product 4.jpg',
      author: 'Dr. Sarah Kiprotich',
      date: '2024-01-15',
      category: 'Platform Launch',
      tags: ['documentation', 'platform', 'traditional knowledge', 'technology'],
      readTime: '5 min read',
      views: 2450,
      featured: true,
    },
    {
      id: 'maasai-ip-protection-success',
      title: 'Historic IP Protection Success for Maasai Traditional Medicine',
      excerpt:
        'Maasai community achieves groundbreaking intellectual property protection for traditional healing formulations, setting precedent for indigenous rights.',
      content: 'Full article content here...',
      image: '/assets/hero image.jpg',
      author: 'James Sankale',
      date: '2024-01-10',
      category: 'IP Protection',
      tags: ['intellectual property', 'Maasai', 'traditional medicine', 'legal'],
      readTime: '4 min read',
      views: 1890,
      featured: false,
    },
    {
      id: 'aloe-cooperative-national-award',
      title: 'Baringo Aloe Cooperative Wins National Innovation Award',
      excerpt:
        "Women's aloe vera cooperative in Baringo County receives prestigious national recognition for outstanding community-led innovation.",
      content: 'Full article content here...',
      image: '/assets/product 5.jpg',
      author: 'Mary Chepkemoi',
      date: '2024-01-05',
      category: 'Awards',
      tags: ['awards', 'innovation', 'women empowerment', 'aloe vera'],
      readTime: '3 min read',
      views: 1650,
      featured: false,
    },
    {
      id: 'international-investment-forum',
      title: 'International Investment Forum Attracts $50M in Commitments',
      excerpt:
        "NPI's first International Natural Products Investment Forum successfully attracts significant funding commitments for community-based projects.",
      content: 'Full article content here...',
      image: '/assets/background.jpg',
      author: 'Dr. Peter Murithi',
      date: '2023-12-20',
      category: 'Investment',
      tags: ['investment', 'funding', 'international', 'forum'],
      readTime: '6 min read',
      views: 2100,
      featured: true,
    },
    {
      id: 'youth-moringa-enterprise',
      title: 'Turkana Youth Transform Moringa into Thriving Enterprise',
      excerpt:
        'Young entrepreneurs in Turkana County develop sustainable moringa processing facility, creating jobs and nutritious products.',
      content: 'Full article content here...',
      image: '/assets/logo.jpg',
      author: 'John Ekale',
      date: '2023-12-15',
      category: 'Youth Empowerment',
      tags: ['youth', 'entrepreneurship', 'moringa', 'Turkana'],
      readTime: '4 min read',
      views: 1420,
      featured: false,
    },
    {
      id: 'research-collaboration-announcement',
      title: 'New Research Collaboration with International Universities',
      excerpt:
        'NPI announces groundbreaking research partnerships with leading universities to advance natural products science.',
      content: 'Full article content here...',
      image: '/assets/product 6.jpg',
      author: 'Prof. Grace Mutindi',
      date: '2023-12-10',
      category: 'Research',
      tags: ['research', 'collaboration', 'universities', 'science'],
      readTime: '5 min read',
      views: 1780,
      featured: false,
    },
    {
      id: 'environmental-conservation-initiative',
      title: 'New Environmental Conservation Initiative Launched',
      excerpt:
        'Comprehensive program to protect biodiversity while supporting sustainable natural products development across Kenya.',
      content: 'Full article content here...',
      image: '/assets/partners structure.jpg',
      author: 'Dr. Samuel Lekorere',
      date: '2023-12-05',
      category: 'Conservation',
      tags: ['conservation', 'biodiversity', 'environment', 'sustainability'],
      readTime: '4 min read',
      views: 1320,
      featured: false,
    },
    {
      id: 'community-training-program',
      title: 'Comprehensive Community Training Program Reaches 1000 Participants',
      excerpt:
        "Milestone achievement as NPI's capacity building programs successfully train over 1000 community members across Kenya.",
      content: 'Full article content here...',
      image: '/assets/product 1.jpg',
      author: 'Catherine Wanjiku',
      date: '2023-11-28',
      category: 'Training',
      tags: ['training', 'capacity building', 'community', 'milestone'],
      readTime: '3 min read',
      views: 980,
      featured: false,
    },
  ],
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')

  const categories = ['All Categories', ...Array.from(new Set(articles.map((a) => a.category)))]

  const filteredArticles = articles.filter((article) => {
    const matchesSearch =
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory =
      selectedCategory === 'All Categories' || article.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const featuredArticles = filteredArticles.filter((a) => a.featured)
  const regularArticles = filteredArticles.filter((a) => !a.featured)

  return (
    <NPISection className="bg-[#E5E1DC]/30">
      <NPISectionHeader>
        <NPISectionTitle className="text-[#34170D]">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#46372A]">{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Search and Filters */}
      <NPICard className="mb-8 border-l-4 border-[#4C6444]">
        <NPICardContent className="p-6">
          <div className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <input
                type="text"
                placeholder="Search news articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
              />
            </div>
          </div>

          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium font-npi">Filter by category:</span>
          </div>
          <div className="grid md:grid-cols-1 gap-4">
            <div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Featured Articles */}
      {featuredArticles.length > 0 && (
        <div className="mb-12">
          <h3 className="text-2xl font-bold mb-6 font-npi">Featured Stories</h3>
          <div className="grid lg:grid-cols-2 gap-8">
            {featuredArticles.map((article, index) => (
              <NPICard
                key={article.id}
                className="overflow-hidden hover:shadow-xl transition-all duration-300 border-l-4 border-[#6E3C19]"
              >
                <div className="relative h-64 w-full">
                  <Image src={article.image} alt={article.title} fill className="object-cover" />
                  <div className="absolute top-4 left-4">
                    <span className="bg-[#A7795E] text-[#E5E1DC] px-3 py-1 text-sm font-medium">
                      Featured
                    </span>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="bg-black/70 text-white px-2 py-1 rounded text-xs font-npi">
                      {article.category}
                    </span>
                  </div>
                </div>

                <NPICardHeader>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(article.date).toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      {article.author}
                    </span>
                    <span className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {article.views.toLocaleString()}
                    </span>
                  </div>
                  <NPICardTitle className="text-xl leading-tight">{article.title}</NPICardTitle>
                </NPICardHeader>

                <NPICardContent>
                  <p className="text-muted-foreground leading-relaxed mb-4 font-npi">
                    {article.excerpt}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex flex-wrap gap-2">
                      {article.tags.slice(0, 3).map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-primary/10 text-primary text-xs rounded font-npi"
                        >
                          {tag}
                        </span>
                      ))}
                      {article.tags.length > 3 && (
                        <span className="text-xs text-muted-foreground font-npi">
                          +{article.tags.length - 3} more
                        </span>
                      )}
                    </div>
                    <span className="text-sm text-muted-foreground font-npi">
                      {article.readTime}
                    </span>
                  </div>
                </NPICardContent>

                <NPICardFooter>
                  <NPIButton asChild variant="primary" className="w-full">
                    <Link href={`/news/${article.id}`}>
                      Read Full Article <ArrowRight className="w-4 h-4 ml-2" />
                    </Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            ))}
          </div>
        </div>
      )}

      {/* Regular Articles */}
      <div className="grid lg:grid-cols-4 gap-6">
        {regularArticles.map((article, index) => {
          // Cycle through bright colors from the 14-color palette
          const colorClasses = [
            'bg-[#6E3C19] text-[#E5E1DC]', // Reddish brown with cream text
            'bg-[#A7795E] text-[#2F2C29]', // Light brown with dark text
            'bg-[#4C6444] text-[#E5E1DC]', // Dark green with cream text
            'bg-[#8A6240] text-[#E5E1DC]', // Medium brown with cream text
          ]
          const colorClass = colorClasses[index % colorClasses.length]

          return (
            <NPICard
              key={article.id}
              className="hover:shadow-lg transition-shadow duration-300 border-l-4 border-[#8D8F78]"
            >
              <div className="relative h-48 w-full">
                <Image src={article.image} alt={article.title} fill className="object-cover" />
                <div className="absolute top-4 right-4">
                  <span className={`${colorClass} px-2 py-1 text-xs font-npi font-medium`}>
                    {article.category}
                  </span>
                </div>
              </div>

              <NPICardHeader>
                <div className="flex items-center gap-2 text-xs text-muted-foreground mb-2">
                  <Calendar className="w-3 h-3" />
                  <span>{new Date(article.date).toLocaleDateString()}</span>
                  <span>•</span>
                  <span>{article.readTime}</span>
                  <span>•</span>
                  <span>{article.views} views</span>
                </div>
                <NPICardTitle className="text-lg leading-tight">{article.title}</NPICardTitle>
              </NPICardHeader>

              <NPICardContent>
                <p className="text-muted-foreground text-sm leading-relaxed mb-4 font-npi">
                  {article.excerpt.substring(0, 120)}...
                </p>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground font-npi">
                    By {article.author}
                  </span>
                  <div className="flex gap-1">
                    {article.tags.slice(0, 2).map((tag, tagIndex) => {
                      const tagColors = [
                        'bg-[#CABA9C] text-[#34170D]', // Light brown with dark text
                        'bg-[#8D8F78] text-[#E5E1DC]', // Olive with cream text
                      ]
                      const tagColorClass = tagColors[tagIndex % tagColors.length]
                      return (
                        <span
                          key={tagIndex}
                          className={`px-1 py-0.5 ${tagColorClass} text-xs font-npi font-medium`}
                        >
                          {tag}
                        </span>
                      )
                    })}
                  </div>
                </div>
              </NPICardContent>

              <NPICardFooter>
                <NPIButton
                  asChild
                  variant="outline"
                  className="w-full border-[#8D8F78] text-[#46372A] hover:bg-[#8D8F78] hover:text-[#E5E1DC]"
                  size="sm"
                >
                  <Link href={`/news/${article.id}`}>
                    Read More <ArrowRight className="w-3 h-3 ml-2" />
                  </Link>
                </NPIButton>
              </NPICardFooter>
            </NPICard>
          )
        })}
      </div>

      {/* Newsletter Signup */}
      <div className="mt-12">
        <NPINewsletter
          title="Stay Informed"
          description="Subscribe to our newsletter to receive the latest news, updates, and insights from Kenya's natural products sector directly in your inbox."
        />
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-muted-foreground font-npi">
          Showing {filteredArticles.length} of {articles.length} articles
        </p>
      </div>
    </NPISection>
  )
}
