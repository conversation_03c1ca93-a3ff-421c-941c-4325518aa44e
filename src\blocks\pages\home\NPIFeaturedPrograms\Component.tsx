'use client'

import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardDescription,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'

interface Project {
  title: string
  description: string
  image: string
  category: string
  location?: string
  status: 'active' | 'completed' | 'upcoming'
  link: string
}

interface NPIFeaturedProjectsProps {
  title?: string
  description?: string
  projects?: Project[]
}

export const NPIFeaturedProjectsBlock: React.FC<NPIFeaturedProjectsProps> = ({
  title = 'Featured Projects',
  description = "Discover our key initiatives transforming Kenya's natural products landscape through community-driven innovation and sustainable development.",
  projects = [
    {
      title: 'Indigenous Knowledge Documentation Project',
      description:
        'Comprehensive documentation of traditional knowledge systems across 47 counties, preserving invaluable cultural heritage for future generations.',
      image: '/assets/product 1.jpg',
      category: 'Knowledge Preservation',
      location: 'Nationwide',
      status: 'active',
      link: '/projects/knowledge-documentation',
    },
    {
      title: 'Community-Based Product Development',
      description:
        'Empowering local communities to develop market-ready products from indigenous plants and traditional practices.',
      image: '/assets/product 2.jpg',
      category: 'Product Development',
      location: 'Multiple Counties',
      status: 'active',
      link: '/projects/community-development',
    },
    {
      title: 'Natural Products Innovation Hub',
      description:
        'State-of-the-art research facility supporting product development, testing, and commercialization of natural products.',
      image: '/assets/product 3.jpg',
      category: 'Research & Innovation',
      location: 'Nairobi',
      status: 'upcoming',
      link: '/projects/innovation-hub',
    },
    {
      title: 'Women & Youth Empowerment Initiative',
      description:
        'Targeted programs supporting women and youth entrepreneurs in the natural products value chain.',
      image: '/assets/product 4.jpg',
      category: 'Capacity Building',
      location: 'Rural Communities',
      status: 'active',
      link: '/projects/youth-empowerment',
    },
    {
      title: 'Market Access & Export Development',
      description:
        'Creating pathways for Kenyan natural products to reach local and international markets.',
      image:
        'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop&crop=center',
      category: 'Market Development',
      location: 'Kenya & International',
      status: 'active',
      link: '/projects/market-access',
    },
    {
      title: 'Intellectual Property Protection',
      description:
        'Safeguarding traditional knowledge and supporting communities in protecting their intellectual property rights.',
      image:
        'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=600&fit=crop&crop=center',
      category: 'Legal & IP',
      location: 'Nationwide',
      status: 'active',
      link: '/projects/ip-protection',
    },
  ],
}) => {
  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return 'bg-gradient-to-r from-green-500 to-emerald-600 text-white border-0 shadow-lg'
      case 'completed':
        return 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-lg'
      case 'upcoming':
        return 'bg-gradient-to-r from-amber-500 to-orange-600 text-white border-0 shadow-lg'
      default:
        return 'bg-gradient-to-r from-gray-500 to-slate-600 text-white border-0 shadow-lg'
    }
  }

  return (
    <NPISection
      size="sm"
      className="bg-gradient-to-br from-[#E5E1DC] via-[#CEC9BC]/30 to-[#CABA9C]/20 relative overflow-hidden"
    >
      {/* Enhanced Vibrant Background */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Animated gradient orbs */}
        <motion.div
          className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-[#6E3C19]/15 to-[#A7795E]/15 rounded-full blur-3xl"
          animate={{
            x: [0, 50, 0],
            y: [0, -30, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-l from-[#4C6444]/12 to-[#102820]/10 rounded-full blur-3xl"
          animate={{
            x: [0, -40, 0],
            y: [0, 25, 0],
            scale: [1, 0.9, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
        {/* Subtle grid pattern */}
        <div
          className="absolute inset-0 opacity-[0.03]"
          style={{
            backgroundImage: `
            linear-gradient(90deg, hsl(var(--primary)) 1px, transparent 1px),
            linear-gradient(hsl(var(--primary)) 1px, transparent 1px)
          `,
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <NPISectionHeader className="text-center mb-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-6 py-3 bg-primary/15 border border-primary/30 text-primary text-sm font-medium mb-4"
          >
            <span className="w-2 h-2 bg-accent mr-3"></span>
            Featured Projects
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-3 bg-gradient-to-r from-[#102820] via-[#4C6444] via-[#8D8F78] to-[#CEC9BC] bg-clip-text text-transparent font-bold text-4xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <NPISectionDescription className="font-light leading-[1.6] text-lg max-w-2xl mx-auto">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {projects.slice(0, 4).map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.7,
                delay: index * 0.15,
                type: 'spring',
                stiffness: 100,
              }}
              whileHover={{ y: -8 }}
            >
              <NPICard
                className={`overflow-hidden shadow-xl border-4 hover:shadow-2xl group transition-all duration-300 flex flex-col aspect-square w-full hover:scale-[1.08] hover:-translate-y-3 ${
                  index % 4 === 0
                    ? 'bg-gradient-to-br from-[#102820] via-[#4C6444] to-[#8D8F78] border-[#4C6444] hover:border-[#102820] hover:shadow-[#4C6444]/50'
                    : index % 4 === 1
                      ? 'bg-gradient-to-br from-[#A7795E] via-[#CABA9C] to-[#8A6240] border-[#A7795E] hover:border-[#8A6240] hover:shadow-[#A7795E]/50'
                      : index % 4 === 2
                        ? 'bg-gradient-to-br from-[#34170D] via-[#6E3C19] to-[#46372A] border-[#6E3C19] hover:border-[#34170D] hover:shadow-[#6E3C19]/50'
                        : 'bg-gradient-to-br from-[#8D8F78] via-[#CEC9BC] to-[#E5E1DC] border-[#8D8F78] hover:border-[#CEC9BC] hover:shadow-[#8D8F78]/50'
                }`}
              >
                {/* Square Image Section - Takes up top half */}
                <div className="relative h-1/2 w-full flex-shrink-0 overflow-hidden">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  />
                  <div
                    className={`absolute inset-0 ${
                      index % 4 === 0
                        ? 'bg-gradient-to-t from-[#102820]/80 via-[#4C6444]/40 to-transparent'
                        : index % 4 === 1
                          ? 'bg-gradient-to-t from-[#8A6240]/80 via-[#A7795E]/40 to-transparent'
                          : index % 4 === 2
                            ? 'bg-gradient-to-t from-[#34170D]/80 via-[#6E3C19]/40 to-transparent'
                            : 'bg-gradient-to-t from-[#8D8F78]/80 via-[#CEC9BC]/40 to-transparent'
                    }`}
                  />
                  <div className="absolute top-2 left-2">
                    <span
                      className={`px-2 py-1 text-xs font-bold shadow-lg ${getStatusColor(project.status)} backdrop-blur-sm`}
                    >
                      {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                    </span>
                  </div>
                  {/* Decorative corner accent */}
                  <div className="absolute top-0 right-0 w-8 h-8 bg-gradient-to-bl from-[#A7795E]/50 via-[#8A6240]/30 to-transparent"></div>
                </div>

                {/* Content Section - Takes up bottom half */}
                <NPICardHeader
                  className={`h-1/2 p-3 flex-shrink-0 flex flex-col justify-between ${
                    index % 4 === 0
                      ? 'bg-gradient-to-r from-[#E5E1DC] via-[#8D8F78]/20 to-[#4C6444]/15'
                      : index % 4 === 1
                        ? 'bg-gradient-to-r from-[#E5E1DC] via-[#CABA9C]/30 to-[#A7795E]/20'
                        : index % 4 === 2
                          ? 'bg-gradient-to-r from-[#E5E1DC] via-[#8A6240]/20 to-[#6E3C19]/15'
                          : 'bg-gradient-to-r from-[#E5E1DC] via-[#CEC9BC]/30 to-[#8D8F78]/20'
                  }`}
                >
                  <div
                    className={`text-xs font-bold uppercase tracking-wide mb-1 ${
                      index % 4 === 0
                        ? 'text-[#102820]'
                        : index % 4 === 1
                          ? 'text-[#8A6240]'
                          : index % 4 === 2
                            ? 'text-[#34170D]'
                            : 'text-[#8D8F78]'
                    }`}
                  >
                    {project.category}
                  </div>
                  <NPICardTitle
                    className={`text-sm font-bold leading-tight transition-colors line-clamp-2 flex-1 flex items-start ${
                      index % 4 === 0
                        ? 'text-[#2F2C29] group-hover:text-[#102820]'
                        : index % 4 === 1
                          ? 'text-[#2F2C29] group-hover:text-[#8A6240]'
                          : index % 4 === 2
                            ? 'text-[#2F2C29] group-hover:text-[#34170D]'
                            : 'text-[#2F2C29] group-hover:text-[#8D8F78]'
                    }`}
                  >
                    {project.title}
                  </NPICardTitle>
                  <div className="flex items-center justify-between mt-2">
                    {project.location && (
                      <div className="text-xs text-[#46372A] font-medium flex items-center gap-1">
                        <span
                          className={`w-1 h-1 rounded-full ${
                            index % 4 === 0
                              ? 'bg-[#4C6444]'
                              : index % 4 === 1
                                ? 'bg-[#A7795E]'
                                : index % 4 === 2
                                  ? 'bg-[#6E3C19]'
                                  : 'bg-[#8D8F78]'
                          }`}
                        ></span>
                        <span className="line-clamp-1">{project.location}</span>
                      </div>
                    )}
                  </div>
                </NPICardHeader>

                {/* Fixed Content Section - Exactly 160px height */}
                <NPICardContent className="h-[160px] px-4 py-3 flex-shrink-0 flex flex-col justify-start">
                  <NPICardDescription className="text-sm leading-[1.5] text-muted-foreground line-clamp-6 h-full overflow-hidden">
                    {project.description}
                  </NPICardDescription>
                </NPICardContent>

                {/* Fixed Footer Section - Exactly 70px height */}
                <NPICardFooter className="h-[70px] px-4 pb-4 pt-0 flex-shrink-0 flex items-center">
                  <NPIButton
                    asChild
                    className="w-full h-[42px] border-2 border-primary text-white hover:bg-gradient-to-r hover:from-primary hover:to-accent hover:text-white font-bold transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 flex items-center justify-center"
                  >
                    <Link
                      href={project.link}
                      className="flex items-center justify-center w-full h-full"
                    >
                      Learn More
                    </Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <NPIButton
            asChild
            className="bg-gradient-to-r from-primary via-accent to-primary hover:from-accent hover:via-primary hover:to-accent text-white font-bold px-10 py-5 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 text-lg"
          >
            <Link href="/projects" className="flex items-center gap-2">
              View All Projects
              <motion.span
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
              >
                →
              </motion.span>
            </Link>
          </NPIButton>
        </motion.div>
      </div>
    </NPISection>
  )
}
