import * as React from 'react'
import { cn } from '@/utilities/ui'
import Image from 'next/image'
import Link from 'next/link'

interface Partner {
  name: string
  logo: string
  url?: string
  description?: string
}

interface NPIPartnersProps extends React.HTMLAttributes<HTMLDivElement> {
  partners: Partner[]
  title?: string
  variant?: 'grid' | 'scroll'
  showNames?: boolean
}

const NPIPartners = React.forwardRef<HTMLDivElement, NPIPartnersProps>(
  ({ className, partners, title, variant = 'grid', showNames = false, ...props }, ref) => {
    return (
      <div ref={ref} className={cn('', className)} {...props}>
        {title && <h3 className="text-xl font-semibold text-center mb-8 font-npi">{title}</h3>}

        {variant === 'grid' ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-8 items-center">
            {partners.map((partner, index) => (
              <NPIPartnerLogo key={index} partner={partner} showName={showNames} />
            ))}
          </div>
        ) : (
          <div className="flex overflow-x-auto space-x-8 pb-4">
            {partners.map((partner, index) => (
              <div key={index} className="flex-shrink-0">
                <NPIPartnerLogo partner={partner} showName={showNames} />
              </div>
            ))}
          </div>
        )}
      </div>
    )
  },
)
NPIPartners.displayName = 'NPIPartners'

interface NPIPartnerLogoProps {
  partner: Partner
  showName?: boolean
  className?: string
}

const NPIPartnerLogo: React.FC<NPIPartnerLogoProps> = ({
  partner,
  showName = false,
  className,
}) => {
  const content = (
    <div
      className={cn(
        'flex flex-col items-center justify-center p-6 rounded-lg hover:bg-card/80 transition-all duration-300 border border-muted/50 hover:border-primary/30 group hover:shadow-md',
        className,
      )}
    >
      <div className="relative w-28 h-20 mb-3">
        <Image
          src={partner.logo}
          alt={`${partner.name} logo`}
          fill
          className="object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 group-hover:scale-105"
        />
      </div>
      {showName && (
        <span className="text-sm text-center text-muted-foreground group-hover:text-foreground font-npi transition-colors duration-300">
          {partner.name}
        </span>
      )}
    </div>
  )

  if (partner.url) {
    return (
      <Link href={partner.url} target="_blank" rel="noopener noreferrer" className="block">
        {content}
      </Link>
    )
  }

  return content
}

export { NPIPartners, NPIPartnerLogo }
