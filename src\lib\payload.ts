import { headers } from 'next/headers'
import { getPayload } from 'payload'
import config from '@payload-config'

// Middleware to initialize Payload and pass it to API routes
// Cache the payload instance
let cachedPayload: any = null

export async function initPayload() {
  if (!cachedPayload) {
    const headersList = headers()

    try {
      cachedPayload = await getPayload({
        // Pass the config
        config,
        // Convert headers into a plain object
        headers: {
          ...Object.fromEntries(headersList.entries()),
        },
      })

      return cachedPayload
    } catch (error) {
      console.error('Error initializing Payload:', error)
      throw error
    }
  }

  return cachedPayload
}
