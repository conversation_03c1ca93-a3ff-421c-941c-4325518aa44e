import React, { Fragment } from 'react'

import { ArchiveBlock } from '@/blocks/ArchiveBlock/Component'
import { CallToActionBlock } from '@/blocks/CallToAction/Component'
import { ContentBlock } from '@/blocks/Content/Component'
import { FormBlock } from '@/blocks/Form/Component'
import { MediaBlock } from '@/blocks/MediaBlock/Component'
// Import from organized page structure
import {
  NPIIntroductionBlock,
  NPIMissionVisionBlock,
  NPIFeaturedProjectsBlock,
  NPISuccessStoriesBlock,
  NPIPartnersBlock,
  NPILatestUpdatesBlock,
} from '@/blocks/pages/home'

import {
  NPIAboutHeroBlock,
  NPIHistoryTimelineBlock,
  NPIOperationsHeroBlock,
  NPIOperationsStructureBlock,
  NPIStrategicAlignmentBlock,
  NPIStrategicAlignmentHeroBlock,
} from '@/blocks/pages/about'

import {
  NPIPartnershipsHeroBlock,
  NPIPartnershipModelsBlock,
  NPIInvestmentOpportunitiesBlock,
  NPIInvestmentOpportunitiesHeroBlock,
  NPIPartnersHeroBlock,
  NPIPartnersShowcaseBlock,
} from '@/blocks/pages/partnerships'

import {
  NPIResourcesLibraryBlock,
  NPIMediaGalleryBlock,
  NPIMediaGalleryHeroBlock,
  NPIMediaGalleryContentBlock,
} from '@/blocks/pages/resources'

import {
  NPIProgramsHeroBlock,
  NPIProgramsListingBlock,
  NPIProjectsHeroBlock,
  NPIProjectsListingBlock,
} from '@/blocks/pages/projects'

import { NPIPillarsHeroBlock, NPIStrategicPillarsBlock } from '@/blocks/pages/strategic-pillars'

import {
  NPISuccessStoriesHeroBlock,
  NPISuccessStoriesGridBlock,
} from '@/blocks/pages/success-stories'

import { NPIContactFormBlock } from '@/blocks/pages/contact'

import { NPIEventsHeroBlock, NPIEventsCalendarBlock } from '@/blocks/pages/events'

import { NPINewsHeroBlock, NPINewsListingBlock } from '@/blocks/pages/news'

import {
  NPIGetInvolvedHeroBlock,
  NPIEngagementOpportunitiesBlock,
} from '@/blocks/pages/get-involved'

import { NPIStatisticsBlock } from '@/blocks/shared'

const blockComponents = {
  archive: ArchiveBlock,
  content: ContentBlock,
  cta: CallToActionBlock,
  formBlock: FormBlock,
  mediaBlock: MediaBlock,
  npiIntroduction: NPIIntroductionBlock,
  npiMissionVision: NPIMissionVisionBlock,
  npiFeaturedProjects: NPIFeaturedProjectsBlock,
  npiSuccessStories: NPISuccessStoriesBlock,
  npiStatistics: NPIStatisticsBlock,
  npiPartners: NPIPartnersBlock,
  npiLatestUpdates: NPILatestUpdatesBlock,
  npiAboutHero: NPIAboutHeroBlock,
  npiHistoryTimeline: NPIHistoryTimelineBlock,
  npiStrategicAlignment: NPIStrategicAlignmentBlock,
  npiOperationsStructure: NPIOperationsStructureBlock,
  npiPillarsHero: NPIPillarsHeroBlock,
  npiStrategicPillars: NPIStrategicPillarsBlock,

  npiProgramsHero: NPIProgramsHeroBlock,
  npiProgramsListing: NPIProgramsListingBlock,
  npiProjectsHero: NPIProjectsHeroBlock,
  npiProjectsListing: NPIProjectsListingBlock,
  npiContactForm: NPIContactFormBlock,
  npiSuccessStoriesHero: NPISuccessStoriesHeroBlock,
  npiSuccessStoriesGrid: NPISuccessStoriesGridBlock,
  npiPartnershipsHero: NPIPartnershipsHeroBlock,
  npiInvestmentOpportunities: NPIInvestmentOpportunitiesBlock,
  npiInvestmentOpportunitiesHero: NPIInvestmentOpportunitiesHeroBlock,
  npiPartnershipModels: NPIPartnershipModelsBlock,
  npiGetInvolvedHero: NPIGetInvolvedHeroBlock,
  npiEngagementOpportunities: NPIEngagementOpportunitiesBlock,
  npiResourcesLibrary: NPIResourcesLibraryBlock,
  npiNewsHero: NPINewsHeroBlock,
  npiNewsListing: NPINewsListingBlock,
  npiEventsCalendar: NPIEventsCalendarBlock,
  npiEventsHero: NPIEventsHeroBlock,
  npiMediaGallery: NPIMediaGalleryBlock,
  npiMediaGalleryHero: NPIMediaGalleryHeroBlock,
  npiMediaGalleryContent: NPIMediaGalleryContentBlock,
  npiOperationsHero: NPIOperationsHeroBlock,
  npiStrategicAlignmentHero: NPIStrategicAlignmentHeroBlock,
  npiPartnersHero: NPIPartnersHeroBlock,
  npiPartnersShowcase: NPIPartnersShowcaseBlock,
}

export const RenderBlocks: React.FC<{
  blocks: Array<{ blockType: keyof typeof blockComponents; [key: string]: any }>
}> = (props) => {
  const { blocks } = props

  const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0

  if (hasBlocks) {
    return (
      <Fragment>
        {blocks.map((block, index) => {
          const { blockType } = block

          if (blockType && blockType in blockComponents) {
            const Block = blockComponents[blockType]

            if (Block) {
              // Add consistent spacing between blocks with proper visual separation
              const isFirstBlock = index === 0
              const isLastBlock = index === blocks.length - 1

              return (
                <section
                  className={`
                    ${isFirstBlock ? 'pt-0' : 'pt-2 lg:pt-4'}
                    ${isLastBlock ? 'pb-0' : 'pb-2 lg:pb-4'}
                    relative
                    min-h-[400px] lg:min-h-[500px]
                    flex items-center
                    ${
                      index % 6 === 0
                        ? 'bg-[#E5E1DC]'
                        : index % 6 === 1
                          ? 'bg-[#8D8F78]/15'
                          : index % 6 === 2
                            ? 'bg-[#CABA9C]/20'
                            : index % 6 === 3
                              ? 'bg-[#4C6444]/12'
                              : index % 6 === 4
                                ? 'bg-[#2F2C29]/8'
                                : 'bg-[#CEC9BC]/25'
                    }
                  `}
                  key={index}
                >
                  <div className="w-full">
                    {/* @ts-expect-error there may be some mismatch between the expected types here */}
                    <Block {...block} disableInnerContainer />
                  </div>
                </section>
              )
            }
          }
          return null
        })}
      </Fragment>
    )
  }

  return null
}
