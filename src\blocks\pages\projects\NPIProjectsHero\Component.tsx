'use client'

import React from 'react'
import Link from 'next/link'
import { NPIParallaxHero } from '@/components/ui/npi-parallax-hero'
import { NPIButton } from '@/components/ui/npi-button'
import { motion } from 'framer-motion'
import { Database, Lightbulb, Users, TrendingUp } from 'lucide-react'

interface NPIProjectsHeroProps {
  title?: string
  subtitle?: string
  totalProjects?: number
  activeProjects?: number
  beneficiaries?: number
}

export const NPIProjectsHeroBlock: React.FC<NPIProjectsHeroProps> = ({
  title = 'Projects & Initiatives',
  subtitle = "Comprehensive projects driving sustainable development through indigenous knowledge preservation, community empowerment, and natural products innovation across Kenya.",
  totalProjects = 35,
  activeProjects = 28,
  beneficiaries = 12000,
}) => {
  const projectTypes = [
    {
      icon: <Database className="w-6 h-6" />,
      title: 'Knowledge Documentation',
      count: '12 Projects',
      description: 'Preserving traditional wisdom',
      color: 'from-[#102820] to-[#4C6444]',
    },
    {
      icon: <Lightbulb className="w-6 h-6" />,
      title: 'Innovation & Development',
      count: '8 Projects',
      description: 'Product commercialization',
      color: 'from-[#6E3C19] to-[#A7795E]',
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: 'Community Empowerment',
      count: '10 Projects',
      description: 'Capacity building initiatives',
      color: 'from-[#8A6240] to-[#CABA9C]',
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: 'Market Development',
      count: '5 Projects',
      description: 'Value chain enhancement',
      color: 'from-[#4D2D18] to-[#8D8F78]',
    },
  ]

  return (
    <NPIParallaxHero
      backgroundVideo="/assets/hero.mp4"
      height="large"
      parallaxSpeed={0.3}
      overlayOpacity={0.7}
      className="relative min-h-[70vh] max-h-[85vh] -mt-16 pt-16"
    >
      {/* Custom Layout Container - Absolute positioning for extreme corners */}
      <div className="absolute inset-0 text-white z-20">
        {/* Top Left Section - Aligned with navbar container */}
        <motion.div
          className="absolute top-0 left-0 pt-26 px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Organization info */}
            <motion.p
              className="text-sm md:text-base text-[#E5E1DC]/90 font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              Natural Products Industry Initiative
            </motion.p>

            {/* Main title */}
            <motion.h1
              className="text-3xl md:text-4xl lg:text-5xl font-bold leading-[1.1] tracking-[-0.02em]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {title}
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              className="text-base md:text-lg text-[#E5E1DC]/80 leading-relaxed max-w-md"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              {subtitle}
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="primary"
                  className="bg-gradient-to-r from-[#102820] via-[#4C6444] to-[#8D8F78] hover:from-[#34170D] hover:via-[#6E3C19] hover:to-[#A7795E] text-[#E5E1DC] font-bold px-8 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-[#CABA9C]/40 hover:border-[#A7795E]/60"
                >
                  <Link href="#projects-listing" className="flex items-center gap-2">
                    View All Projects
                    <motion.span
                      animate={{ x: [0, 3, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                    >
                      →
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-2 border-[#A7795E] bg-gradient-to-r from-[#CABA9C]/30 to-[#8A6240]/25 text-[#E5E1DC] hover:bg-gradient-to-r hover:from-[#A7795E] hover:to-[#CABA9C] hover:text-[#141311] backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold hover:border-[#CABA9C]"
                >
                  <Link href="/partnerships" className="flex items-center gap-2">
                    Partner With Us
                    <motion.span
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                    >
                      🤝
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom Right Section - Project Statistics */}
        <motion.div
          className="absolute bottom-0 right-0 pb-16 px-4 sm:px-6 lg:px-8 max-w-7xl ml-auto"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="max-w-2xl">
            {/* Statistics Cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {projectTypes.map((type, index) => (
                <motion.div
                  key={type.title}
                  className={`bg-gradient-to-br ${type.color} p-4 backdrop-blur-md border border-white/20 hover:border-white/40 transition-all duration-300`}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 + index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className="text-[#E5E1DC]">{type.icon}</div>
                    <div className="text-xs font-bold text-[#E5E1DC]">{type.count}</div>
                  </div>
                  <h3 className="text-sm font-bold text-[#E5E1DC] mb-1">{type.title}</h3>
                  <p className="text-xs text-[#E5E1DC]/80">{type.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Key Metrics */}
            <motion.div
              className="text-right space-y-2"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <div className="flex justify-end gap-8">
                <div>
                  <div className="text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#A7795E] to-[#CABA9C]">
                    {totalProjects}
                  </div>
                  <div className="text-xs text-[#E5E1DC]/80">Total Projects</div>
                </div>
                <div>
                  <div className="text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#4C6444] to-[#8D8F78]">
                    {activeProjects}
                  </div>
                  <div className="text-xs text-[#E5E1DC]/80">Active Projects</div>
                </div>
                <div>
                  <div className="text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#6E3C19] to-[#8A6240]">
                    {beneficiaries.toLocaleString()}+
                  </div>
                  <div className="text-xs text-[#E5E1DC]/80">Beneficiaries</div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </NPIParallaxHero>
  )
}
