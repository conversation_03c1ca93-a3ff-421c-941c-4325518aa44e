// Test script for Payload CMS authentication endpoints
// Run with: node examples/test-auth-endpoints.js

const BASE_URL = 'http://localhost:3000/api/users'

// Test credentials - make sure you have a user with these credentials
const TEST_EMAIL = '<EMAIL>'
const TEST_PASSWORD = 'password123'

let authToken = null

async function testLogin() {
  console.log('🔐 Testing Login...')
  
  try {
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL,
        password: TEST_PASSWORD,
      }),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Login successful!')
      console.log('User:', data.user)
      console.log('Token expires at:', new Date(data.exp * 1000))
      authToken = data.token
      return true
    } else {
      console.log('❌ Login failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Login error:', error.message)
    return false
  }
}

async function testMe() {
  console.log('\n👤 Testing Get Current User...')
  
  if (!authToken) {
    console.log('❌ No auth token available')
    return false
  }

  try {
    const response = await fetch(`${BASE_URL}/me`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Get current user successful!')
      console.log('User:', data.user)
      return true
    } else {
      console.log('❌ Get current user failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Get current user error:', error.message)
    return false
  }
}

async function testRefreshToken() {
  console.log('\n🔄 Testing Refresh Token...')
  
  if (!authToken) {
    console.log('❌ No auth token available')
    return false
  }

  try {
    const response = await fetch(`${BASE_URL}/refresh-token`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Token refresh successful!')
      console.log('New token expires at:', new Date(data.exp * 1000))
      authToken = data.token // Update with new token
      return true
    } else {
      console.log('❌ Token refresh failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Token refresh error:', error.message)
    return false
  }
}

async function testLogout() {
  console.log('\n🚪 Testing Logout...')
  
  if (!authToken) {
    console.log('❌ No auth token available')
    return false
  }

  try {
    const response = await fetch(`${BASE_URL}/logout`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Logout successful!')
      console.log('Message:', data.message)
      authToken = null
      return true
    } else {
      console.log('❌ Logout failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Logout error:', error.message)
    return false
  }
}

async function testForgotPassword() {
  console.log('\n🔑 Testing Forgot Password...')
  
  try {
    const response = await fetch(`${BASE_URL}/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL,
      }),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Forgot password request successful!')
      console.log('Message:', data.message)
      return true
    } else {
      console.log('❌ Forgot password failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Forgot password error:', error.message)
    return false
  }
}

async function runTests() {
  console.log('🧪 Starting Payload CMS Authentication Tests')
  console.log('='.repeat(50))
  
  // Test login
  const loginSuccess = await testLogin()
  
  if (loginSuccess) {
    // Test authenticated endpoints
    await testMe()
    await testRefreshToken()
    
    // Test logout
    await testLogout()
    
    // Test that we can't access protected endpoints after logout
    console.log('\n🔒 Testing access after logout...')
    await testMe() // This should fail
  }
  
  // Test forgot password (doesn't require auth)
  await testForgotPassword()
  
  console.log('\n' + '='.repeat(50))
  console.log('🏁 Tests completed!')
}

// Run the tests
runTests().catch(console.error)
