import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'Projects & Initiatives - Natural Products Industry Initiative',
  description:
    "Explore NPI's comprehensive projects and initiatives driving sustainable development through indigenous knowledge preservation, community empowerment, and natural products innovation across Kenya.",
}

const projectsPageLayout = [
  {
    blockType: 'npiProjectsHero' as const,
  },
  {
    blockType: 'npiProjectsListing' as const,
    id: 'projects-listing',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Projects Impact',
    variant: 'secondary',
  },
]

export default function ProjectsPage() {
  return (
    <article>
      <PageClient />
      <RenderBlocks blocks={projectsPageLayout} />
    </article>
  )
}
