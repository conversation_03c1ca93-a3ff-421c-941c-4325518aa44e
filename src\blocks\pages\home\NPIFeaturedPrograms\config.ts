import type { Block } from 'payload'

export const NPIFeaturedProjects: Block = {
  slug: 'npiFeaturedProjects',
  interfaceName: 'NPIFeaturedProjectsBlock',
  fields: [
    {
      name: 'title',
      type: 'text',
      defaultValue: 'Featured Projects',
      label: 'Title',
    },
    {
      name: 'description',
      type: 'text',
      defaultValue: "Discover our key initiatives transforming Kenya's natural products landscape.",
      label: 'Description',
    },
  ],
  labels: {
    plural: 'NPI Featured Projects Blocks',
    singular: 'NPI Featured Projects Block',
  },
}
