'use client'

import React from 'react'
import { NPISection } from '@/components/ui/npi-section'
import {
  BentoGrid,
  BentoCard,
  BentoCardHeader,
  BentoCardTitle,
  BentoCardContent,
} from '@/components/ui/npi-bento-grid'
import { Users, MapPin, Briefcase, Database, TrendingUp, Award } from 'lucide-react'
import { motion } from 'framer-motion'

interface NPIStatisticsBlockProps {
  title?: string
  variant?: 'default' | 'primary' | 'secondary' | 'accent' | 'pattern'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'tight'
}

export const NPIStatisticsBlock: React.FC<NPIStatisticsBlockProps> = ({
  title,
  variant = 'pattern',
  size = 'md',
}) => {
  const statistics = [
    {
      value: '47',
      label: 'Counties Covered',
      description: 'Nationwide reach across Kenya',
      icon: <MapPin className="w-8 h-8" />,
    },
    {
      value: '2,500+',
      label: 'Community Members',
      description: 'Directly engaged in programs',
      icon: <Users className="w-8 h-8" />,
    },
    {
      value: '150+',
      label: 'Investment Opportunities',
      description: 'Ready for development',
      icon: <Briefcase className="w-8 h-8" />,
    },
    {
      value: '1,200+',
      label: 'Indigenous Knowledge Assets',
      description: 'Documented and preserved',
      icon: <Database className="w-8 h-8" />,
    },
    {
      value: '85%',
      label: 'Income Increase',
      description: 'Average for participating communities',
      icon: <TrendingUp className="w-8 h-8" />,
    },
    {
      value: '25+',
      label: 'Awards & Recognition',
      description: 'For innovation and impact',
      icon: <Award className="w-8 h-8" />,
    },
  ]

  return (
    <NPISection size={size} className="bg-tertiary relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 right-1/3 w-80 h-80 bg-primary/5 blur-3xl"
          animate={{
            x: [0, -30, 0],
            y: [0, 20, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-secondary/5 blur-3xl"
          animate={{
            x: [0, 40, 0],
            y: [0, -25, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
      </div>

      <div className="relative z-10">
        {title && (
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="inline-flex items-center px-4 py-2 bg-primary/10 backdrop-blur-md border border-primary/20 text-primary text-sm font-medium mb-4"
            >
              <span className="w-2 h-2 bg-primary mr-2 animate-pulse"></span>
              Impact & Growth
            </motion.div>
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="npi-heading-2 mb-3 font-npi text-primary font-bold leading-[1.1] tracking-[-0.02em]"
            >
              {title}
            </motion.h2>
          </div>
        )}

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {statistics.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`
                ${index % 3 === 0 ? 'bg-white/80 border-primary/20 hover:border-primary/40' : ''}
                ${index % 3 === 1 ? 'bg-white/60 border-secondary/20 hover:border-secondary/40' : ''}
                ${index % 3 === 2 ? 'bg-white/80 border-accent/20 hover:border-accent/40' : ''}
                backdrop-blur-sm border-2 p-4 shadow-lg hover:shadow-xl transition-all duration-300 group
              `}
            >
              <div className="flex items-center gap-4 mb-4">
                <div
                  className={`p-3 transition-all duration-300 shadow-lg group-hover:scale-110 ${
                    index % 3 === 0
                      ? 'bg-gradient-to-br from-primary to-primary/80 text-white'
                      : index % 3 === 1
                        ? 'bg-gradient-to-br from-secondary to-secondary/80 text-white'
                        : 'bg-gradient-to-br from-accent to-accent/80 text-white'
                  }`}
                >
                  {stat.icon}
                </div>
                <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
                  {stat.label}
                </h3>
              </div>
              <div
                className={`text-4xl font-bold mb-3 font-npi transition-colors duration-300 ${
                  index % 3 === 0
                    ? 'text-primary'
                    : index % 3 === 1
                      ? 'text-secondary'
                      : 'text-accent'
                }`}
              >
                {stat.value}
              </div>
              <p className="text-foreground/80 font-npi font-light leading-[1.6]">
                {stat.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </NPISection>
  )
}
