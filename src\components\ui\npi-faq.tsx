'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from './npi-card'

interface FAQItem {
  question: string
  answer: string
}

interface NPIFAQProps {
  title?: string
  description?: string
  faqs?: FAQItem[]
  className?: string
}

export const NPIFAQ: React.FC<NPIFAQProps> = ({
  title = 'Frequently Asked Questions',
  description = 'Find answers to common questions about the Natural Products Industry Initiative.',
  faqs = [
    {
      question: 'What is the Natural Products Industry Initiative (NPI)?',
      answer:
        "The NPI is a comprehensive program designed to harness Kenya's rich natural heritage for sustainable economic growth. We focus on developing natural products industries through research, innovation, and community empowerment.",
    },
    {
      question: 'How can I access the IKIA database?',
      answer:
        'The Indigenous Knowledge Information Archive (IKIA) database can be accessed through our online portal. Contact us for access credentials and training on how to use the database effectively.',
    },
    {
      question: 'What partnership opportunities are available?',
      answer:
        "We offer various partnership opportunities including research collaborations, technology transfer, capacity building programs, and joint ventures. Contact our partnerships team to explore opportunities that align with your organization's goals.",
    },
    {
      question: 'How do I apply for NPI programs?',
      answer:
        'Application processes vary by program. Visit our programs section for specific requirements and application procedures, or contact us directly for guidance on the most suitable programs for your needs.',
    },
    {
      question: 'What support does NPI provide to communities?',
      answer:
        'We provide technical assistance, capacity building, market linkage support, and technology transfer to help communities develop sustainable natural products enterprises while preserving traditional knowledge.',
    },
    {
      question: 'How can researchers collaborate with NPI?',
      answer:
        'Researchers can collaborate through joint research projects, access to our facilities and databases, participation in conferences, and contribution to our knowledge base. Contact our research team to discuss collaboration opportunities.',
    },
  ],
  className = '',
}) => {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index],
    )
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Header */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center gap-2 mb-3">
          <HelpCircle className="w-6 h-6 text-[#25718A]" />
          <h2 className="text-2xl font-bold text-[#8A3E25] font-npi">{title}</h2>
        </div>
        <p className="text-base text-[#725242] font-npi max-w-2xl mx-auto">{description}</p>
      </div>

      {/* FAQ Items */}
      <div className="space-y-3">
        {faqs.map((faq, index) => (
          <NPICard
            key={index}
            className="bg-white border-2 border-[#725242]/30 hover:border-[#725242]/60 transition-all duration-300 hover:shadow-lg hover:scale-[1.02]"
          >
            <NPICardHeader className="p-0">
              <button
                onClick={() => toggleItem(index)}
                className="w-full text-left p-4 flex items-center justify-between hover:bg-[#725242]/10 transition-all duration-200"
                aria-expanded={openItems.includes(index)}
              >
                <NPICardTitle className="text-base font-semibold text-[#8A3E25] font-npi pr-3">
                  {faq.question}
                </NPICardTitle>
                <motion.div
                  animate={{ rotate: openItems.includes(index) ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                  className="flex-shrink-0"
                >
                  <ChevronDown className="w-4 h-4 text-[#25718A]" />
                </motion.div>
              </button>
            </NPICardHeader>
            <AnimatePresence>
              {openItems.includes(index) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                  className="overflow-hidden"
                >
                  <NPICardContent className="px-4 pb-4 pt-0">
                    <div className="border-t border-[#25718A]/20 pt-3">
                      <p className="text-[#725242] font-npi leading-relaxed text-sm">
                        {faq.answer}
                      </p>
                    </div>
                  </NPICardContent>
                </motion.div>
              )}
            </AnimatePresence>
          </NPICard>
        ))}
      </div>

      {/* Contact CTA */}
      <div className="mt-6 text-center">
        <NPICard className="bg-[#25718A] border-2 border-[#25718A]/50">
          <NPICardContent className="p-4">
            <p className="text-white font-npi mb-2 text-sm font-semibold">
              Can't find the answer you're looking for?
            </p>
            <p className="text-sm text-white/90 font-npi">
              Contact our team directly for personalized assistance and detailed information about
              our programs and services.
            </p>
          </NPICardContent>
        </NPICard>
      </div>
    </div>
  )
}
